# 🖥️ LSB Log Monitor - Console Application

## 🎯 **Chuyển từ Windows Service sang Console Application**

### **Lợi ích của Console Application:**
- ✅ **Dễ debug** - Thấy real-time logs trên console
- ✅ **Dễ deploy** - Chỉ cần copy files, không cần installer
- ✅ **Dễ test** - Chạy trực tiếp, không cần install service
- ✅ **Interactive** - <PERSON><PERSON> thể nhập commands trong khi chạy
- ✅ **Portable** - Chạy được trên bất kỳ máy nào có .NET

---

## 🚀 **Cách sử dụng:**

### **Quick Start:**
```cmd
# Build và chạy
build-console.bat

# Hoặc chạy trực tiếp
run-console.bat

# Development mode
run-dev.bat
```

### **Manual Build:**
```cmd
# Build
dotnet build LSB.LogMonitor.Service -c Release

# Publish
dotnet publish LSB.LogMonitor.Service -c Release -o dist

# Run
cd dist
LSB.LogMonitor.Service.exe
```

---

## 🎮 **Console Commands:**

Khi application đang chạy, bạn có thể nhập các commands:

- **`q` / `quit` / `exit`** - Thoát application
- **`test`** - Chạy test function
- **`status`** - Hiển thị trạng thái hiện tại
- **`help` / `?`** - Hiển thị danh sách commands
- **`Ctrl+C`** - Graceful shutdown

### **Ví dụ sử dụng:**
```
========================================
LSB Log Monitor - Console Application
========================================
Started at: 2024-01-15 14:30:25
Working Directory: D:\LSB.LogMonitor.Service\dist

Commands:
  Ctrl+C  - Stop application
  'q'     - Quit
  'test'  - Run test
  'status'- Show status
========================================

🚀 LSB Log Monitor started. Type 'help' for commands.

> test
🔧 Running test...
✅ Test completed successfully!

> status
📊 Status: Running since 14:30:25
📁 Working Directory: D:\LSB.LogMonitor.Service\dist

> q
🛑 Quitting...
🔄 Shutting down...
✅ LSB Log Monitor stopped
```

---

## 📁 **File Structure:**

### **Development:**
```
LSB.LogMonitor.Service/
├── Program.cs              # Console application entry point
├── Services/               # Business logic services
├── Configuration/          # Configuration classes
├── appsettings.json       # Configuration file
└── LSB.LogMonitor.Service.csproj
```

### **Published Application:**
```
dist/
├── LSB.LogMonitor.Service.exe     # Main executable
├── LSB.LogMonitor.Service.dll     # Application DLL
├── appsettings.json               # Configuration
├── *.dll                          # Dependencies
└── *.json                         # Runtime configs
```

---

## 🔧 **Configuration:**

### **appsettings.json:**
```json
{
  "LogMonitor": {
    "LogDirectory": "C:\\ProgramData\\DaBox\\LSBHub\\Logs",
    "CheckIntervalMinutes": 5,
    "TelegramBotToken": "your-bot-token",
    "TelegramChatId": "your-chat-id"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning"
    }
  }
}
```

### **Environment Variables:**
```cmd
set LogMonitor__TelegramBotToken=your-token
set LogMonitor__TelegramChatId=your-chat-id
```

---

## 🚀 **Deployment:**

### **Cách 1: Copy Files**
1. Build: `build-console.bat`
2. Copy thư mục `dist` sang máy target
3. Chạy: `LSB.LogMonitor.Service.exe`

### **Cách 2: Self-Contained**
```cmd
# Build self-contained (không cần .NET runtime)
dotnet publish LSB.LogMonitor.Service -c Release -r win-x64 --self-contained true -o dist-standalone

# Copy dist-standalone sang máy target và chạy
```

### **Cách 3: Chạy như Windows Service (Optional)**
```cmd
# Sử dụng NSSM (Non-Sucking Service Manager)
nssm install "LSB Log Monitor" "C:\path\to\LSB.LogMonitor.Service.exe"
nssm start "LSB Log Monitor"
```

---

## 🛠️ **Development:**

### **Debug trong Visual Studio:**
1. Mở `LSB.LogMonitor.Service.sln`
2. Set `LSB.LogMonitor.Service` as startup project
3. Press F5 để debug

### **Hot Reload:**
```cmd
cd LSB.LogMonitor.Service
dotnet watch run
```

### **Test Arguments:**
```cmd
# Chạy test mode
LSB.LogMonitor.Service.exe --test

# Hoặc trong development
dotnet run -- --test
```

---

## 🚨 **Troubleshooting:**

### **❌ "Configuration file not found"**
**Giải pháp:** Đảm bảo `appsettings.json` ở cùng thư mục với .exe

### **❌ "Missing DLL dependencies"**
**Giải pháp:** 
1. Publish với `--self-contained true`
2. Hoặc cài .NET 8.0 Runtime trên máy target

### **❌ "Access denied to log directory"**
**Giải pháp:** Chạy với quyền Administrator hoặc thay đổi log directory

### **❌ "Telegram API errors"**
**Giải pháp:** Kiểm tra bot token và chat ID trong configuration

---

## 📊 **Monitoring:**

### **Console Output:**
- Real-time logs hiển thị trên console
- Colored output cho dễ đọc
- Timestamp cho mỗi log entry

### **Log Files:**
- Application logs: Theo configuration
- Error logs: Console + file (nếu configured)

### **Performance:**
- Memory usage hiển thị trong status command
- CPU usage có thể monitor qua Task Manager

---

## 🎯 **So sánh với Windows Service:**

| Feature | Windows Service | Console Application |
|---------|----------------|-------------------|
| **Installation** | Cần installer/admin | Copy files |
| **Debugging** | Khó debug | Dễ debug |
| **Logs** | Event Log/Files | Console + Files |
| **Startup** | Auto với Windows | Manual/Script |
| **Interaction** | Không | Interactive commands |
| **Deployment** | Phức tạp | Đơn giản |
| **Development** | Khó test | Dễ test |

---

## ✅ **Kết luận:**

Console Application phù hợp hơn cho:
- **Development và testing**
- **Debugging issues**
- **Quick deployment**
- **Interactive monitoring**
- **Portable solutions**

Chỉ cần chạy `build-console.bat` và bạn có một application hoàn chỉnh! 🎉
