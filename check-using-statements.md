# 🔧 Check and Fix Using Statements

## ❌ **Common Missing Using Statements**

### **Program.cs should have:**
```csharp
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using LSB.LogMonitor.Service.Contracts;
using LSB.LogMonitor.Service.Configuration;
using LSB.LogMonitor.Services;
```

### **LogMonitorWorker.cs should have:**
```csharp
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using LSB.LogMonitor.Service.Models;
using LSB.LogMonitor.Service.Contracts;
```

### **ConfigService.cs should have:**
```csharp
using Microsoft.Extensions.Logging;
using System.Text.Json;
using LSB.LogMonitor.Service.Models;
```

### **TelegramService.cs should have:**
```csharp
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.Net.Http;
using System.Text.Json;
using LSB.LogMonitor.Service.Contracts;
using LSB.LogMonitor.Service.Models;
```

### **LogService.cs should have:**
```csharp
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using LSB.LogMonitor.Service.Contracts;
using LSB.LogMonitor.Service.Configuration;
using YamlDotNet.Serialization;
```

## ✅ **Quick Fix Steps:**

### **1. Add Missing Using Statements**
Open each file and add the required using statements at the top.

### **2. Verify Project File**
Ensure `LSB.LogMonitor.Service.csproj` contains all these packages with correct versions:
```xml
<PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.1" />
<PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.1" />
<PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.2" />
<PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
<PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.1" />
<PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="8.0.0" />
<PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="8.0.2" />
<PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.1" />
<PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.2" />
<PackageReference Include="Microsoft.Extensions.Logging.Console" Version="8.0.1" />
<PackageReference Include="Microsoft.Extensions.Options" Version="8.0.2" />
<PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="8.0.0" />
<PackageReference Include="Microsoft.Extensions.Http" Version="8.0.1" />
```

**⚠️ Important Version Notes:**
- `Microsoft.Extensions.Configuration.Json` must be `8.0.1` (not 8.0.0)
- `Microsoft.Extensions.DependencyInjection.Abstractions` must be `8.0.2` (not 8.0.1)
- These versions resolve package downgrade warnings

### **3. Fix Version Conflicts**
If you get package downgrade warnings, run:
```cmd
fix-version-conflicts.bat
```

This script will:
- Update package versions to resolve conflicts
- Clean previous builds
- Restore packages
- Build the project

### **4. Build Commands**
```cmd
# Fix version conflicts and build
fix-version-conflicts.bat

# Or manual steps:
# Restore packages
dotnet restore LSB.LogMonitor.Service

# Clean build
dotnet clean LSB.LogMonitor.Service

# Build
dotnet build LSB.LogMonitor.Service -c Release

# Run
dotnet run --project LSB.LogMonitor.Service
```

### **4. Visual Studio Steps**
1. Open `LSB.LogMonitor.Service.sln`
2. Right-click solution → **Restore NuGet Packages**
3. **Build** → **Clean Solution**
4. **Build** → **Rebuild Solution**
5. Press **F5** to run

## 🚨 **If Still Getting Errors:**

### **Check Namespace Issues:**
- Ensure all service classes are in correct namespaces
- Check interface implementations match

### **Check Target Framework:**
- Project should target `net8.0`
- All packages should be compatible with .NET 8

### **Check File Structure:**
```
LSB.LogMonitor.Service/
├── Program.cs
├── Services/
│   ├── LogMonitorWorker.cs
│   ├── ConfigService.cs
│   ├── TelegramService.cs
│   └── LogService.cs
├── Contracts/
│   └── (Interface files)
├── Models/
│   └── (Model files)
└── Configuration/
    └── (Configuration files)
```

## 🎯 **Expected Result:**
After fixing all using statements and restoring packages, you should be able to:
1. Build without errors
2. Run the console application
3. See the interactive console interface
4. Use commands like `test`, `status`, `q`
