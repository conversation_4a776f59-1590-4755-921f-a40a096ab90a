{"format": 1, "restore": {"D:\\CodeBase\\LSB.LogMonitor.Service\\LSB.LogMonitor.Service\\LSB.LogMonitor.Service.csproj": {}}, "projects": {"D:\\CodeBase\\LSB.LogMonitor.Service\\LSB.LogMonitor.Service\\LSB.LogMonitor.Service.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\CodeBase\\LSB.LogMonitor.Service\\LSB.LogMonitor.Service\\LSB.LogMonitor.Service.csproj", "projectName": "LSB.LogMonitor.Service", "projectPath": "D:\\CodeBase\\LSB.LogMonitor.Service\\LSB.LogMonitor.Service\\LSB.LogMonitor.Service.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\CodeBase\\LSB.LogMonitor.Service\\LSB.LogMonitor.Service\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.Extensions.Hosting": {"target": "Package", "version": "[6.0.1, )"}, "Microsoft.Extensions.Hosting.WindowsServices": {"target": "Package", "version": "[6.0.2, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[6.0.0, )"}, "System.Diagnostics.PerformanceCounter": {"target": "Package", "version": "[6.0.1, )"}, "System.Management": {"target": "Package", "version": "[6.0.0, )"}, "System.Text.Json": {"target": "Package", "version": "[6.0.9, )"}, "YamlDotNet": {"target": "Package", "version": "[13.7.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.Net.Sdk.Compilers.Toolset", "version": "[9.0.300, 9.0.300]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}}}