@echo off
echo ========================================
echo LSB Log Monitor Service - Build with Heat
echo ========================================
echo.

echo Using WiX Heat tool to automatically harvest all dependencies
echo.

echo [1/5] Cleaning previous builds...
if exist "ServiceFiles.wxs" del "ServiceFiles.wxs"
if exist "bin" rmdir /s /q "bin"
if exist "obj" rmdir /s /q "obj"

echo.
echo [2/5] Building main service project...
msbuild "..\LSB.LogMonitor.Service\LSB.LogMonitor.Service.csproj" /p:Configuration=Release /p:Platform="Any CPU" /v:minimal
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Failed to build main service project
    pause
    exit /b 1
)

echo.
echo [3/5] Publishing service project with all dependencies...
msbuild "..\LSB.LogMonitor.Service\LSB.LogMonitor.Service.csproj" /p:Configuration=Release /p:Platform="Any CPU" /t:Publish /v:minimal
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Failed to publish service project
    pause
    exit /b 1
)

echo.
echo [4/5] Running Heat to harvest files and building installer...
echo Heat will automatically:
echo - Scan the publish directory
echo - Generate ServiceFiles.wxs with all components
echo - Apply XSLT transform to add service installation
echo - Build the complete installer
echo.

msbuild "LSB.LogMonitor.Service.Setup.wixproj" /p:Configuration=Release /v:minimal
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Failed to build installer project
    echo.
    echo Check the following:
    echo - WiX Toolset is installed
    echo - Heat.exe is available
    echo - Publish directory exists and contains files
    echo - XSLT transform is valid
    echo.
    pause
    exit /b 1
)

echo.
echo [5/5] Verifying installer...
if exist "bin\Release\LSB.LogMonitor.Service.Setup.msi" (
    echo ✅ Installer created successfully with Heat!
    echo.
    echo File: bin\Release\LSB.LogMonitor.Service.Setup.msi
    for %%A in ("bin\Release\LSB.LogMonitor.Service.Setup.msi") do echo Size: %%~zA bytes
    echo.
    
    if exist "ServiceFiles.wxs" (
        echo ✅ Heat generated ServiceFiles.wxs
        echo Checking component count...
        findstr /c:"<Component" ServiceFiles.wxs > temp_count.txt
        for /f %%i in ('find /c /v "" ^< temp_count.txt') do set COMPONENT_COUNT=%%i
        del temp_count.txt
        echo Found !COMPONENT_COUNT! components automatically harvested
    ) else (
        echo ⚠️  ServiceFiles.wxs not found
    )
    
    echo.
    echo Heat automatically included:
    echo - ALL DLL dependencies from publish directory
    echo - Configuration files
    echo - Runtime files
    echo - Service registration for LSB.LogMonitor.Service.exe
    echo.
    echo Ready for deployment!
) else (
    echo ❌ Installer file not found
    echo Expected: bin\Release\LSB.LogMonitor.Service.Setup.msi
)

echo.
echo ========================================
echo Build with Heat Complete
echo ========================================
pause
