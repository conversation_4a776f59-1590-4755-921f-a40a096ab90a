# 🎯 Visual Studio Build Guide - LSB Telemetry Service Installer

## 🚀 **<PERSON><PERSON><PERSON> build bằng Visual Studio với đầy đủ DLL (Đơn giản nhất)**

### **Quick Start:**
1. **Kiểm tra môi trường:** `LSB.LogMonitor.Service.Setup\setup-vs-environment.bat`
2. **Mở Visual Studio:** `LSB.LogMonitor.Service.sln`
3. **Build:** Right-click solution → **Rebuild Solution**
4. **Kết quả:** `LSB.LogMonitor.Service.Setup\bin\Release\LSB.LogMonitor.Service.Setup.msi`

---

## 📋 **Y<PERSON>u cầu hệ thống:**

### **<PERSON><PERSON><PERSON> mềm cần thiết:**
- ✅ **Visual Studio 2019/2022** (Community/Professional/Enterprise)
- ✅ **WiX Toolset v3.11+** - Download: https://wixtoolset.org/releases/
- ✅ **WiX Toolset Visual Studio Extension**
- ✅ **.NET 8.0 SDK**

### **Kiểm tra nhanh:**
```cmd
cd LSB.LogMonitor.Service.Setup
setup-vs-environment.bat
```

---

## 🔧 **Cách build trong Visual Studio:**

### **Bước 1: Mở Solution**
1. Mở **Visual Studio**
2. **File > Open > Project/Solution**
3. Chọn `LSB.LogMonitor.Service.sln`

### **Bước 2: Cấu hình Build**
1. Chọn **Release** configuration (dropdown trên toolbar)
2. Chọn **Any CPU** platform
3. Đảm bảo cả 2 projects hiển thị trong Solution Explorer:
   - `LSB.LogMonitor.Service` (C# Service)
   - `LSB.LogMonitor.Service.Setup` (WiX Installer)

### **Bước 3: Build Installer**
**Cách 1 - Build toàn bộ (Khuyến nghị):**
- Right-click **Solution** → **Rebuild Solution**

**Cách 2 - Build riêng installer:**
- Right-click **LSB.LogMonitor.Service.Setup** → **Rebuild**

### **Bước 4: Theo dõi Build Process**
Trong **Output Window** (View > Output), bạn sẽ thấy:
```
[VS BUILD] Publishing service project to ensure all dependencies are available...
[VS BUILD] Running Heat to harvest files from publish directory...
[VS BUILD] Heat completed. Generated ServiceFiles.wxs with all dependencies.
Build succeeded.
```

---

## 🎯 **Cách hoạt động của Heat (Tự động):**

### **Quy trình tự động trong Visual Studio:**
1. **Auto-Publish Service** → Tạo thư mục `publish` với tất cả DLLs
2. **Heat Harvesting** → Scan và tạo `ServiceFiles.wxs` với tất cả components
3. **XSLT Transform** → Thêm service installation cho .exe file
4. **WiX Compile** → Build MSI installer hoàn chỉnh

### **Files được tạo tự động:**
- `LSB.LogMonitor.Service\bin\Release\net8.0\publish\` - Tất cả dependencies
- `LSB.LogMonitor.Service.Setup\ServiceFiles.wxs` - Generated components
- `LSB.LogMonitor.Service.Setup\bin\Release\*.msi` - Final installer

---

## ✅ **Kết quả sau khi build:**

### **File MSI Installer:**
```
📁 LSB.LogMonitor.Service.Setup\bin\Release\
   📄 LSB.LogMonitor.Service.Setup.msi (>10MB với tất cả DLLs)
```

### **Cài đặt Service:**
1. **Run MSI as Administrator**
2. **Service được cài đặt:** `C:\Program Files\LSB\TelemetryService\`
3. **Service tự động start:** `LSBTelemetryService`
4. **Không thiếu DLL nào!**

---

## 🚨 **Troubleshooting:**

### **❌ "Heat not found"**
**Giải pháp:**
1. Cài đặt WiX Toolset: https://wixtoolset.org/releases/
2. Restart Visual Studio
3. Chạy `setup-vs-environment.bat` để kiểm tra

### **❌ "Build failed - Publish directory not found"**
**Giải pháp:**
1. Clean Solution: **Build > Clean Solution**
2. Rebuild: **Build > Rebuild Solution**

### **❌ "ServiceFiles.wxs not found"**
**Giải pháp:**
1. Kiểm tra Output window cho Heat errors
2. Đảm bảo WiX Toolset đã cài đặt đúng
3. Rebuild installer project

### **❌ "Missing DLLs after installation"**
**Nguyên nhân:** Heat không harvest đúng
**Giải pháp:**
1. Kiểm tra `publish` directory có đầy đủ files
2. Xem `ServiceFiles.wxs` có chứa tất cả DLLs
3. Clean và rebuild solution

---

## 🎉 **Lợi ích của Heat trong Visual Studio:**

### **✅ Tự động hoàn toàn:**
- Không cần liệt kê DLLs thủ công
- Tự động bao gồm dependencies mới
- Tự động tạo GUIDs cho components

### **✅ Dễ bảo trì:**
- Thêm NuGet package → Heat tự động bao gồm
- Không cần cập nhật installer thủ công
- Luôn đồng bộ với service project

### **✅ Không bỏ sót:**
- Thu thập TẤT CẢ files từ publish directory
- Bao gồm cả native dependencies
- Đảm bảo service chạy được sau cài đặt

---

## 📝 **Files quan trọng:**

### **Không được edit thủ công:**
- `ServiceFiles.wxs` - Generated by Heat
- `bin\Release\net8.0\publish\*` - Generated by dotnet publish

### **Có thể customize:**
- `Product.wxs` - Installer configuration
- `ServiceFiles.xslt` - Heat transform rules
- `LSB.LogMonitor.Service.Setup.wixproj` - Build configuration

---

## 🎯 **Tóm tắt:**

**Chỉ cần 3 bước:**
1. ✅ **Setup environment** - Chạy `setup-vs-environment.bat`
2. ✅ **Open Visual Studio** - Mở `LSB.LogMonitor.Service.sln`
3. ✅ **Build** - Right-click solution → Rebuild Solution

**Kết quả:**
- 🎉 **MSI installer hoàn chỉnh** với tất cả 40+ DLLs
- 🎉 **Service tự động cài đặt và start**
- 🎉 **Không thiếu dependencies nào**

**Heat làm tất cả công việc nặng nhọc cho bạn!** 🚀
