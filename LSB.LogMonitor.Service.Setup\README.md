# LSB Telemetry Service - Windows Installer

## Overview
This WiX installer creates a Windows Service for the LSB Log Monitor Service (renamed to LSB Telemetry Service for deployment).

## Fixed Issues
- ✅ **ICE30 Error**: Component reference counting conflicts resolved
- ✅ **Missing Dependencies**: All 48 components now included
- ✅ **System.Diagnostics.EventLog.Messages.dll**: Critical dependency added
- ✅ **Publish Directory**: Using complete published output with all dependencies

## Components Included (48 total)
1. **Service Executable**: LSB.LogMonitor.Service.exe with Windows Service registration
2. **Configuration Files**: appsettings.json, appsettings.Development.json
3. **Runtime Files**: deps.json, runtimeconfig.json, pdb
4. **Microsoft Extensions (24 DLLs)**:
   - Configuration (Abstractions, Binder, CommandLine, EnvironmentVariables, FileExtensions, Json, UserSecrets)
   - DependencyInjection (Abstractions)
   - Diagnostics (Abstractions)
   - FileProviders (Abstractions, Physical)
   - FileSystemGlobbing
   - Hosting (Abstractions, WindowsServices)
   - Http
   - Logging (Abstractions, Configuration, Console, Debug, EventLog, EventSource)
   - Options (ConfigurationExtensions)
   - Primitives
5. **System Libraries (16 DLLs)**:
   - CodeDom
   - Configuration.ConfigurationManager
   - Diagnostics (EventLog, EventLog.Messages, PerformanceCounter)
   - IO.Pipelines
   - Management
   - Security.Cryptography.ProtectedData
   - ServiceProcess.ServiceController
   - Text (Encodings.Web, Json)
6. **Third Party (1 DLL)**: YamlDotNet

## Service Configuration
- **Service Name**: LSBTelemetryService
- **Display Name**: LSB Telemetry Service
- **Description**: LSB Telemetry Service - System monitoring
- **Start Type**: Automatic
- **Account**: Local System
- **Installation Path**: C:\Program Files\LSB\TelemetryService\

## Build Instructions

### Prerequisites
1. WiX Toolset v3.11 or newer
2. Visual Studio 2019/2022 or MSBuild
3. .NET 8.0 SDK

### Build Steps
1. **Test Service** (optional):
   ```cmd
   test-service.bat
   ```

2. **Build Complete Installer**:
   ```cmd
   build-complete.bat
   ```

3. **Manual Build**:
   ```cmd
   # Build and publish service
   msbuild ..\LSB.LogMonitor.Service\LSB.LogMonitor.Service.csproj /p:Configuration=Release /t:Publish
   
   # Build installer
   msbuild LSB.LogMonitor.Service.Setup.wixproj /p:Configuration=Release
   ```

## Installation
1. Run `LSB.LogMonitor.Service.Setup.msi` as Administrator
2. Follow the installation wizard
3. Service will be automatically installed and started

## Verification
After installation, verify the service:
```cmd
sc query LSBTelemetryService
```

## Troubleshooting

### Service Won't Start
- Check Windows Event Log for errors
- Verify all DLL dependencies are installed
- Ensure Local System account has necessary permissions

### Missing Dependencies
- Rebuild with publish target: `/t:Publish`
- Check that all 48 components are in the MSI

### Manual Uninstall
If MSI uninstaller fails:
```cmd
uninstall-service.bat
```

## Files
- `Product.wxs` - Main installer definition
- `build-complete.bat` - Complete build script
- `test-service.bat` - Test service before installation
- `uninstall-service.bat` - Manual uninstall script
- `Generate-WixComponents.ps1` - Auto-generate components from build output

## Notes
- Uses publish directory for complete dependency resolution
- All components have unique GUIDs to prevent conflicts
- Supports automatic upgrades
- Service configured with failure recovery actions
