@echo off
echo ========================================
echo Test LSB Log Monitor Console Application
echo ========================================
echo.

echo [1/3] Building application...
dotnet build LSB.LogMonitor.Service\LSB.LogMonitor.Service.csproj -c Release -v minimal
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Build failed
    pause
    exit /b 1
)

echo ✅ Build successful
echo.

echo [2/3] Running test mode...
cd LSB.LogMonitor.Service\bin\Release\net8.0
LSB.LogMonitor.Service.exe --test

echo.
echo [3/3] Quick interactive test...
echo Starting application for 10 seconds...
echo.

timeout /t 2 /nobreak >nul

echo Starting LSB Log Monitor...
echo (Application will auto-stop after a few seconds for testing)
echo.

start /b LSB.LogMonitor.Service.exe
set APP_PID=%ERRORLEVEL%

echo Application started. Waiting 5 seconds...
timeout /t 5 /nobreak >nul

echo.
echo Stopping application...
taskkill /f /im LSB.LogMonitor.Service.exe >nul 2>&1

echo.
echo ========================================
echo Test completed!
echo ========================================
echo.

echo To run the application normally:
echo   run-console.bat
echo.
echo To run in development mode:
echo   run-dev.bat
echo.
echo To build for deployment:
echo   build-console.bat
echo.

pause
