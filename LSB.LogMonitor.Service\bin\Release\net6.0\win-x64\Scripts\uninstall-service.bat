﻿@echo off
setlocal enabledelayedexpansion

echo ======================================
echo  LSB Log Monitor Service Uninstaller
echo ======================================
echo.

REM Kiểm tra quyền Administrator
net session >nul 2>&1
if %errorLevel% NEQ 0 (
    echo ERROR: This script requires Administrator privileges!
    echo Please run as Administrator.
    echo.
    pause
    exit /b 1
)

set "SERVICE_NAME=LSB.LogMonitor.Service"
set "SERVICE_DISPLAY_NAME=LSB Log Monitor Service"

echo Service to remove: %SERVICE_DISPLAY_NAME%
echo Service name: %SERVICE_NAME%
echo.

echo Checking if service exists...
sc query "%SERVICE_NAME%" >nul 2>&1
if %errorLevel% NEQ 0 (
    echo ℹ Service "%SERVICE_NAME%" is not installed on this system.
    echo Nothing to uninstall.
    echo.
    echo If you're looking for a different service, you can:
    echo • Check Services Manager (services.msc) for installed services
    echo • Use command: sc query type= service state= all
    echo.
    echo Press any key to exit...
    pause >nul
    exit /b 0
)

echo ✓ Service found. Getting service information...
sc qc "%SERVICE_NAME%"
echo.

REM Hiển thị trạng thái hiện tại
echo Current service status:
sc query "%SERVICE_NAME%"
echo.

echo Proceeding with service removal...
echo.

echo Step 1: Stopping the service...
sc stop "%SERVICE_NAME%"

if %errorLevel% EQU 0 (
    echo ✓ Service stop command sent successfully.
    echo Waiting for service to fully stop...
    
    REM Đợi service dừng hoàn toàn
    set /a "counter=0"
    :wait_loop
    timeout /t 1 /nobreak >nul
    sc query "%SERVICE_NAME%" | find "STOPPED" >nul
    if %errorLevel% EQU 0 (
        echo ✓ Service stopped completely.
        goto :service_stopped
    )
    
    set /a "counter+=1"
    if %counter% LSS 10 (
        echo Waiting for service to stop... (%counter%/10)
        goto :wait_loop
    )
    
    echo ⚠ Service may still be stopping. Proceeding with removal...
    
) else (
    sc query "%SERVICE_NAME%" | find "STOPPED" >nul
    if %errorLevel% EQU 0 (
        echo ℹ Service was already stopped.
    ) else (
        echo ⚠ WARNING: Could not stop service or service may still be running.
        echo This might cause the removal to fail.
        echo.
        echo You can try:
        echo 1. Wait a moment and run this script again
        echo 2. Restart your computer and then run this script
        echo 3. Use Task Manager to end any related processes
        echo.
        set /p "CONTINUE=Continue with removal anyway? (y/n): "
        if /i "!CONTINUE!" NEQ "y" (
            echo Operation cancelled by user.
            pause
            exit /b 1
        )
    )
)

:service_stopped
echo.
echo Step 2: Removing the service...
sc delete "%SERVICE_NAME%"

if %errorLevel% EQU 0 (
    echo ✓ Service removal command executed successfully.
    echo.
    echo Verifying removal...
    timeout /t 2 /nobreak >nul
    
    sc query "%SERVICE_NAME%" >nul 2>&1
    if %errorLevel% NEQ 0 (
        echo ✓ Service has been completely removed from the system.
        echo.
        echo ====================================
        echo ✅ UNINSTALLATION COMPLETED SUCCESSFULLY!
        echo ====================================
        echo.
        echo What was removed:
        echo • Service Name: %SERVICE_NAME%
        echo • Display Name: %SERVICE_DISPLAY_NAME%
        echo • Service registration from Windows Service Manager
        echo.
        echo What remains:
        echo • Application files in: %~dp0..\bin\Release\net8.0\
        echo • Configuration files (appsettings.json, etc.)
        echo • Log files (if any were created)
        echo.
        echo These files can be safely deleted manually if no longer needed.
        echo The service will no longer start automatically with Windows.
    ) else (
        echo ⚠ Service removal command succeeded, but service may still be visible.
        echo This is normal and the service should disappear after a system restart.
    )
) else (
    echo ❌ ERROR: Failed to remove service!
    echo Error code: %errorLevel%
    echo.
    echo Possible causes:
    echo • Service is still running or in use
    echo • Another process is accessing the service
    echo • Insufficient permissions
    echo • Windows Service Control Manager is busy
    echo.
    echo Troubleshooting options:
    echo 1. Restart your computer and run this script again
    echo 2. Check Task Manager for any related processes and end them
    echo 3. Use Services Manager (services.msc) to manually stop and delete:
    echo    - Open Services Manager (services.msc)
    echo    - Find "%SERVICE_DISPLAY_NAME%"
    echo    - Right-click → Stop (if running)
    echo    - Right-click → Properties → General → Startup type → Disabled
    echo    - Restart computer, then try this script again
    echo.
    echo 4. Check Windows Event Viewer for detailed error information:
    echo    - Press Win+R, type 'eventvwr.msc'
    echo    - Navigate to Windows Logs → System
    echo    - Look for Service Control Manager errors
)

echo.
echo Press any key to exit...
pause >nul