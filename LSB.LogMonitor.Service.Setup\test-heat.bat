@echo off
echo ========================================
echo Test Heat Tool
echo ========================================
echo.

echo Testing Heat harvesting manually...
echo.

set PUBLISH_DIR=..\LSB.LogMonitor.Service\bin\Release\net8.0\publish

if not exist "%PUBLISH_DIR%" (
    echo ERROR: Publish directory not found: %PUBLISH_DIR%
    echo Please publish the project first:
    echo   msbuild ..\LSB.LogMonitor.Service\LSB.LogMonitor.Service.csproj /p:Configuration=Release /t:Publish
    pause
    exit /b 1
)

echo Publish directory found: %PUBLISH_DIR%
echo.

echo Files in publish directory:
dir "%PUBLISH_DIR%" /b
echo.

echo Running Heat manually...
heat dir "%PUBLISH_DIR%" -cg ServiceFiles -gg -scom -sreg -sfrag -srd -dr INSTALLFOLDER -out ServiceFiles_Test.wxs

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Heat failed
    pause
    exit /b 1
)

echo.
echo ✅ Heat completed successfully!
echo Generated file: ServiceFiles_Test.wxs
echo.

if exist "ServiceFiles_Test.wxs" (
    echo Checking component count...
    findstr /c:"<Component" ServiceFiles_Test.wxs > temp_count.txt
    for /f %%i in ('find /c /v "" ^< temp_count.txt') do set COMPONENT_COUNT=%%i
    del temp_count.txt
    echo Found !COMPONENT_COUNT! components
    echo.
    
    echo First few lines of generated file:
    head -20 ServiceFiles_Test.wxs 2>nul || (
        echo [First 20 lines]
        more +1 ServiceFiles_Test.wxs | findstr /n ".*" | findstr "^[1-9]:" | findstr "^1[0-9]:" | findstr "^[12][0-9]:"
    )
) else (
    echo ❌ ServiceFiles_Test.wxs not generated
)

echo.
pause
