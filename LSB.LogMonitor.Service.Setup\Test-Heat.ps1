# Test Heat Tool for WiX
param(
    [Parameter()]
    [switch]$Publish
)

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Test Heat Tool for WiX" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

if ($Publish) {
    Write-Host "[1/3] Publishing service project..." -ForegroundColor Yellow
    $publishResult = Start-Process -FilePath "msbuild" -ArgumentList @(
        "..\LSB.LogMonitor.Service\LSB.LogMonitor.Service.csproj",
        "/p:Configuration=Release",
        "/p:Platform=Any CPU",
        "/t:Publish",
        "/v:minimal"
    ) -Wait -PassThru -NoNewWindow
    
    if ($publishResult.ExitCode -ne 0) {
        Write-Host "ERROR: Failed to publish service project" -ForegroundColor Red
        exit 1
    }
    Write-Host "✅ Publish completed" -ForegroundColor Green
    Write-Host ""
}

Write-Host "[2/3] Checking publish directory..." -ForegroundColor Yellow
$publishDir = "..\LSB.LogMonitor.Service\bin\Release\net8.0\publish"

if (-not (Test-Path $publishDir)) {
    Write-Host "ERROR: Publish directory not found: $publishDir" -ForegroundColor Red
    Write-Host "Run with -Publish parameter to publish first" -ForegroundColor Yellow
    exit 1
}

Write-Host "✅ Publish directory found: $publishDir" -ForegroundColor Green
$files = Get-ChildItem -Path $publishDir -File | Where-Object { $_.Name -notlike "*Scripts*" }
Write-Host "Found $($files.Count) files in publish directory" -ForegroundColor White
Write-Host ""

Write-Host "[3/3] Testing Heat harvesting..." -ForegroundColor Yellow

# Test Heat command
$heatArgs = @(
    "dir", $publishDir,
    "-cg", "ServiceFiles",
    "-gg",
    "-scom",
    "-sreg", 
    "-sfrag",
    "-srd",
    "-dr", "INSTALLFOLDER",
    "-out", "ServiceFiles_Test.wxs"
)

Write-Host "Running: heat $($heatArgs -join ' ')" -ForegroundColor Gray

try {
    $heatResult = Start-Process -FilePath "heat" -ArgumentList $heatArgs -Wait -PassThru -NoNewWindow
    
    if ($heatResult.ExitCode -ne 0) {
        Write-Host "ERROR: Heat failed with exit code $($heatResult.ExitCode)" -ForegroundColor Red
        exit 1
    }
    
    Write-Host "✅ Heat completed successfully!" -ForegroundColor Green
    
    if (Test-Path "ServiceFiles_Test.wxs") {
        $content = Get-Content "ServiceFiles_Test.wxs"
        $componentCount = ($content | Select-String "<Component").Count
        Write-Host "Generated ServiceFiles_Test.wxs with $componentCount components" -ForegroundColor White
        
        # Check for service executable
        $serviceExe = $content | Select-String "LSB.LogMonitor.Service.exe"
        if ($serviceExe) {
            Write-Host "✅ Service executable found in generated file" -ForegroundColor Green
        } else {
            Write-Host "⚠️  Service executable not found" -ForegroundColor Yellow
        }
        
        Write-Host ""
        Write-Host "Sample of generated content (first 20 lines):" -ForegroundColor Cyan
        $content | Select-Object -First 20 | ForEach-Object { Write-Host "  $_" -ForegroundColor Gray }
        
    } else {
        Write-Host "❌ ServiceFiles_Test.wxs not generated" -ForegroundColor Red
    }
    
} catch {
    Write-Host "ERROR: Failed to run Heat - $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Make sure WiX Toolset is installed and Heat.exe is in PATH" -ForegroundColor Yellow
    exit 1
}

Write-Host ""
Write-Host "Heat test completed!" -ForegroundColor Green
