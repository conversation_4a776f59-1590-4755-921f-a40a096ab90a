@echo off
echo ========================================
echo Fix Package Version Conflicts
echo ========================================
echo.

echo Fixed package versions in project file:
echo.
echo ✅ Microsoft.Extensions.Configuration.Json: 8.0.0 → 8.0.1
echo ✅ Microsoft.Extensions.DependencyInjection.Abstractions: 8.0.1 → 8.0.2
echo.

echo These changes resolve the package downgrade warnings.
echo.

echo [1/4] Cleaning previous builds...
if exist "LSB.LogMonitor.Service\bin" (
    rmdir /s /q "LSB.LogMonitor.Service\bin"
    echo Removed bin directory
)
if exist "LSB.LogMonitor.Service\obj" (
    rmdir /s /q "LSB.LogMonitor.Service\obj"
    echo Removed obj directory
)

echo.
echo [2/4] Restoring NuGet packages...
dotnet restore LSB.LogMonitor.Service\LSB.LogMonitor.Service.csproj
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Package restore failed
    echo.
    echo Try these steps:
    echo 1. Open Visual Studio
    echo 2. Right-click solution ^> Restore NuGet Packages
    echo 3. Build ^> Clean Solution
    echo 4. Build ^> Rebuild Solution
    pause
    exit /b 1
)

echo.
echo [3/4] Building project...
dotnet build LSB.LogMonitor.Service\LSB.LogMonitor.Service.csproj -c Release
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Build failed
    echo.
    echo Check the error messages above.
    echo If you still get "type not found" errors, check:
    echo 1. Using statements in all .cs files
    echo 2. Namespace declarations
    echo 3. Interface implementations
    pause
    exit /b 1
)

echo.
echo [4/4] Testing build output...
if exist "LSB.LogMonitor.Service\bin\Release\net8.0\LSB.LogMonitor.Service.exe" (
    echo ✅ Executable created successfully
    for %%A in ("LSB.LogMonitor.Service\bin\Release\net8.0\LSB.LogMonitor.Service.exe") do echo File size: %%~zA bytes
) else (
    echo ❌ Executable not found
    echo Build may have failed silently
)

echo.
echo ========================================
echo Version Conflict Fix Complete!
echo ========================================
echo.

if exist "LSB.LogMonitor.Service\bin\Release\net8.0\LSB.LogMonitor.Service.exe" (
    echo ✅ Build successful! You can now:
    echo.
    echo 1. Run the console application:
    echo    run-console.bat
    echo.
    echo 2. Test the application:
    echo    test-console.bat
    echo.
    echo 3. Run in development mode:
    echo    run-dev.bat
    echo.
    echo 4. Publish for deployment:
    echo    build-console.bat
) else (
    echo ❌ Build failed. Please check error messages above.
    echo.
    echo Common issues:
    echo 1. Missing using statements
    echo 2. .NET SDK not installed
    echo 3. Package restore failed
    echo.
    echo Try building in Visual Studio for better error messages.
)

echo.
pause
