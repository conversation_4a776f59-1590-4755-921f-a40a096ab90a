@echo off
echo ========================================
echo Build LSB Log Monitor Console Application
echo ========================================
echo.

echo [1/3] Cleaning previous builds...
dotnet clean LSB.LogMonitor.Service\LSB.LogMonitor.Service.csproj
if exist "LSB.LogMonitor.Service\bin" rmdir /s /q "LSB.LogMonitor.Service\bin"
if exist "LSB.LogMonitor.Service\obj" rmdir /s /q "LSB.LogMonitor.Service\obj"

echo.
echo [2/3] Building Release version...
dotnet build LSB.LogMonitor.Service\LSB.LogMonitor.Service.csproj -c Release
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Build failed
    pause
    exit /b 1
)

echo.
echo [3/3] Publishing self-contained application...
dotnet publish LSB.LogMonitor.Service\LSB.LogMonitor.Service.csproj -c Release -o "dist" --self-contained false
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Publish failed
    pause
    exit /b 1
)

echo.
echo ========================================
echo Build completed successfully!
echo ========================================
echo.

echo Output locations:
echo - Build output: LSB.LogMonitor.Service\bin\Release\net8.0\
echo - Published app: dist\
echo.

if exist "dist\LSB.LogMonitor.Service.exe" (
    echo ✅ Executable created: dist\LSB.LogMonitor.Service.exe
    for %%A in ("dist\LSB.LogMonitor.Service.exe") do echo File size: %%~zA bytes
    echo.
    
    echo Files in dist directory:
    dir dist /b | findstr /v "ref"
    echo.
    
    echo To run the application:
    echo   cd dist
    echo   LSB.LogMonitor.Service.exe
    echo.
    echo Or use: run-console.bat
) else (
    echo ❌ Executable not found in dist directory
)

echo.
pause
