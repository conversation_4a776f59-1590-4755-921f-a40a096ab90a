@echo off
echo ========================================
echo LSB Log Monitor Service - Complete Build
echo ========================================
echo.

echo [1/3] Building main service project...
msbuild "..\LSB.LogMonitor.Service\LSB.LogMonitor.Service.csproj" /p:Configuration=Release /p:Platform="Any CPU" /v:minimal
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Failed to build main service project
    pause
    exit /b 1
)

echo.
echo [2/3] Building installer project...
msbuild "LSB.LogMonitor.Service.Setup.wixproj" /p:Configuration=Release /v:minimal
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Failed to build installer project
    echo.
    echo Check the following:
    echo - WiX Toolset is installed
    echo - All DLL files exist in the target directory
    echo - No component ID conflicts
    echo.
    pause
    exit /b 1
)

echo.
echo [3/3] Verifying installer...
if exist "bin\Release\LSB.LogMonitor.Service.Setup.msi" (
    echo ✅ Installer created successfully!
    echo.
    echo File: bin\Release\LSB.LogMonitor.Service.Setup.msi
    for %%A in ("bin\Release\LSB.LogMonitor.Service.Setup.msi") do echo Size: %%~zA bytes
    echo.
    echo The installer now includes ALL 47 components:
    echo - 1 Service executable with service registration
    echo - 6 Configuration and runtime files
    echo - 40 DLL dependencies
    echo.
    echo Ready for deployment!
) else (
    echo ❌ Installer file not found
    echo Expected: bin\Release\LSB.LogMonitor.Service.Setup.msi
)

echo.
echo ========================================
echo Build Complete
echo ========================================
pause
