@echo off
echo ========================================
echo LSB Log Monitor Service - Test Service
echo ========================================
echo.

echo Testing service executable before installation...
echo.

set SERVICE_DIR=..\LSB.LogMonitor.Service\bin\Release\net8.0\publish

if not exist "%SERVICE_DIR%\LSB.LogMonitor.Service.exe" (
    echo ERROR: Service executable not found
    echo Please build and publish the project first:
    echo   msbuild ..\LSB.LogMonitor.Service\LSB.LogMonitor.Service.csproj /p:Configuration=Release /t:Publish
    pause
    exit /b 1
)

echo Checking dependencies...
echo.

set MISSING_FILES=0

echo Checking critical DLLs:
if not exist "%SERVICE_DIR%\System.Diagnostics.EventLog.dll" (
    echo   ❌ System.Diagnostics.EventLog.dll - MISSING
    set MISSING_FILES=1
) else (
    echo   ✅ System.Diagnostics.EventLog.dll - OK
)

if not exist "%SERVICE_DIR%\System.Diagnostics.EventLog.Messages.dll" (
    echo   ❌ System.Diagnostics.EventLog.Messages.dll - MISSING
    set MISSING_FILES=1
) else (
    echo   ✅ System.Diagnostics.EventLog.Messages.dll - OK
)

if not exist "%SERVICE_DIR%\Microsoft.Extensions.Hosting.WindowsServices.dll" (
    echo   ❌ Microsoft.Extensions.Hosting.WindowsServices.dll - MISSING
    set MISSING_FILES=1
) else (
    echo   ✅ Microsoft.Extensions.Hosting.WindowsServices.dll - OK
)

if not exist "%SERVICE_DIR%\YamlDotNet.dll" (
    echo   ❌ YamlDotNet.dll - MISSING
    set MISSING_FILES=1
) else (
    echo   ✅ YamlDotNet.dll - OK
)

echo.

if %MISSING_FILES%==1 (
    echo ❌ Some dependencies are missing!
    echo Please ensure the project is published correctly.
    pause
    exit /b 1
)

echo ✅ All critical dependencies found!
echo.

echo Testing service startup (console mode)...
echo Press Ctrl+C to stop the test
echo.

cd "%SERVICE_DIR%"
LSB.LogMonitor.Service.exe

echo.
echo Service test completed.
pause
