@echo off
echo ========================================
echo LSB Telemetry Service - Manual Uninstall
echo ========================================
echo.

echo This script will manually uninstall the LSB Telemetry Service
echo Use this if the MSI uninstaller is not working properly
echo.

pause

echo Stopping service...
sc stop LSBTelemetryService
timeout /t 3 /nobreak >nul

echo Deleting service...
sc delete LSBTelemetryService

echo.
echo Checking service status...
sc query LSBTelemetryService
if %ERRORLEVEL%==1060 (
    echo ✅ Service successfully removed
) else (
    echo ⚠️  Service may still exist
)

echo.
echo Removing installation directory...
if exist "C:\Program Files\LSB\TelemetryService" (
    rmdir /s /q "C:\Program Files\LSB\TelemetryService"
    if exist "C:\Program Files\LSB\TelemetryService" (
        echo ⚠️  Could not remove installation directory
        echo Please remove manually: C:\Program Files\LSB\TelemetryService
    ) else (
        echo ✅ Installation directory removed
    )
) else (
    echo ✅ Installation directory not found
)

echo.
echo Manual uninstall completed.
echo.
pause
