﻿<?xml version="1.0" encoding="utf-8"?><wixPdb version="3.0.3200.0" xmlns="http://schemas.microsoft.com/wix/2006/pdbs"><wixOutput type="Product" codepage="1252" version="3.0.2002.0" xmlns="http://schemas.microsoft.com/wix/2006/outputs"><tableDefinitions xmlns="http://schemas.microsoft.com/wix/2006/tables"><tableDefinition name="_Streams" unreal="yes"><columnDefinition name="Name" type="string" length="62" primaryKey="yes" /><columnDefinition name="Data" type="object" length="0" nullable="yes" /></tableDefinition><tableDefinition name="_SummaryInformation"><columnDefinition name="PropertyId" type="number" length="2" primaryKey="yes" /><columnDefinition name="Value" type="localized" length="255" escapeIdtCharacters="yes" /></tableDefinition><tableDefinition name="_Validation"><columnDefinition name="Table" type="string" length="32" primaryKey="yes" category="identifier" description="Name of table" /><columnDefinition name="Column" type="string" length="32" primaryKey="yes" category="identifier" description="Name of column" /><columnDefinition name="Nullable" type="string" length="4" set="Y;N" description="Whether the column is nullable" /><columnDefinition name="MinValue" type="number" length="4" nullable="yes" minValue="-2147483647" maxValue="2147483647" description="Minimum value allowed" /><columnDefinition name="MaxValue" type="number" length="4" nullable="yes" minValue="-2147483647" maxValue="2147483647" description="Maximum value allowed" /><columnDefinition name="KeyTable" type="string" length="255" nullable="yes" category="identifier" description="For foreign key, Name of table to which data must link" /><columnDefinition name="KeyColumn" type="number" length="2" nullable="yes" minValue="1" maxValue="32" description="Column to which foreign key connects" /><columnDefinition name="Category" type="string" length="32" nullable="yes" set="Text;Formatted;Template;Condition;Guid;Path;Version;Language;Identifier;Binary;UpperCase;LowerCase;Filename;Paths;AnyPath;WildCardFilename;RegPath;CustomSource;Property;Cabinet;Shortcut;FormattedSDDLText;Integer;DoubleInteger;TimeDate;DefaultDir" description="String category" /><columnDefinition name="Set" type="string" length="255" nullable="yes" category="text" description="Set of values that are permitted" /><columnDefinition name="Description" type="string" length="255" nullable="yes" category="text" description="Description of column" /></tableDefinition><tableDefinition name="AdminExecuteSequence"><columnDefinition name="Action" type="string" length="72" primaryKey="yes" category="identifier" description="Name of action to invoke, either in the engine or the handler DLL." /><columnDefinition name="Condition" type="string" length="255" nullable="yes" localizable="yes" category="condition" description="Optional expression which skips the action if evaluates to expFalse.If the expression syntax is invalid, the engine will terminate, returning iesBadActionData." /><columnDefinition name="Sequence" type="number" length="2" nullable="yes" minValue="-4" maxValue="32767" description="Number that determines the sort order in which the actions are to be executed.  Leave blank to suppress action." /></tableDefinition><tableDefinition name="AdminUISequence"><columnDefinition name="Action" type="string" length="72" primaryKey="yes" category="identifier" description="Name of action to invoke, either in the engine or the handler DLL." /><columnDefinition name="Condition" type="string" length="255" nullable="yes" localizable="yes" category="condition" description="Optional expression which skips the action if evaluates to expFalse.If the expression syntax is invalid, the engine will terminate, returning iesBadActionData." /><columnDefinition name="Sequence" type="number" length="2" nullable="yes" minValue="-4" maxValue="32767" description="Number that determines the sort order in which the actions are to be executed.  Leave blank to suppress action." /></tableDefinition><tableDefinition name="AdvtExecuteSequence"><columnDefinition name="Action" type="string" length="72" primaryKey="yes" category="identifier" description="Name of action to invoke, either in the engine or the handler DLL." /><columnDefinition name="Condition" type="string" length="255" nullable="yes" localizable="yes" category="condition" description="Optional expression which skips the action if evaluates to expFalse.If the expression syntax is invalid, the engine will terminate, returning iesBadActionData." /><columnDefinition name="Sequence" type="number" length="2" nullable="yes" minValue="-4" maxValue="32767" description="Number that determines the sort order in which the actions are to be executed.  Leave blank to suppress action." /></tableDefinition><tableDefinition name="Component" createSymbols="yes"><columnDefinition name="Component" type="string" length="72" primaryKey="yes" modularize="column" category="identifier" description="Primary key used to identify a particular component record." /><columnDefinition name="ComponentId" type="string" length="38" nullable="yes" category="guid" description="A string GUID unique to this component, version, and language." /><columnDefinition name="Directory_" type="string" length="72" modularize="column" keyTable="Directory" keyColumn="1" category="identifier" description="Required key of a Directory table record. This is actually a property name whose value contains the actual path, set either by the AppSearch action or with the default setting obtained from the Directory table." /><columnDefinition name="Attributes" type="number" length="2" description="Remote execution option, one of irsEnum" /><columnDefinition name="Condition" type="string" length="255" nullable="yes" localizable="yes" modularize="condition" category="condition" description="A conditional statement that will disable this component if the specified condition evaluates to the 'True' state. If a component is disabled, it will not be installed, regardless of the 'Action' state associated with the component." /><columnDefinition name="KeyPath" type="string" length="72" nullable="yes" modularize="column" keyTable="File;Registry;ODBCDataSource" keyColumn="1" category="identifier" description="Either the primary key into the File table, Registry table, or ODBCDataSource table. This extract path is stored when the component is installed, and is used to detect the presence of the component and to return the path to it." /></tableDefinition><tableDefinition name="CustomAction" createSymbols="yes"><columnDefinition name="Action" type="string" length="72" primaryKey="yes" modularize="column" category="identifier" description="Primary key, name of action, normally appears in sequence table unless private use." /><columnDefinition name="Type" type="number" length="2" minValue="1" maxValue="32767" description="The numeric custom action type, consisting of source location, code type, entry, option flags." /><columnDefinition name="Source" type="string" length="72" nullable="yes" modularize="column" category="customSource" description="The table reference of the source of the code." /><columnDefinition name="Target" type="string" length="255" nullable="yes" localizable="yes" modularize="property" category="formatted" description="Excecution parameter, depends on the type of custom action" escapeIdtCharacters="yes" /><columnDefinition name="ExtendedType" type="number" length="4" nullable="yes" minValue="0" maxValue="2147483647" description="A numeric custom action type that extends code type or option flags of the Type column." /></tableDefinition><tableDefinition name="Directory" createSymbols="yes"><columnDefinition name="Directory" type="string" length="72" primaryKey="yes" modularize="column" category="identifier" description="Unique identifier for directory entry, primary key. If a property by this name is defined, it contains the full path to the directory." /><columnDefinition name="Directory_Parent" type="string" length="72" nullable="yes" modularize="column" keyTable="Directory" keyColumn="1" category="identifier" description="Reference to the entry in this table specifying the default parent directory. A record parented to itself or with a Null parent represents a root of the install tree." /><columnDefinition name="DefaultDir" type="localized" length="255" category="defaultDir" description="The default sub-path under parent's path." /></tableDefinition><tableDefinition name="Feature" createSymbols="yes"><columnDefinition name="Feature" type="string" length="38" primaryKey="yes" category="identifier" description="Primary key used to identify a particular feature record." /><columnDefinition name="Feature_Parent" type="string" length="38" nullable="yes" keyTable="Feature" keyColumn="1" category="identifier" description="Optional key of a parent record in the same table. If the parent is not selected, then the record will not be installed. Null indicates a root item." /><columnDefinition name="Title" type="localized" length="64" nullable="yes" category="text" description="Short text identifying a visible feature item." escapeIdtCharacters="yes" /><columnDefinition name="Description" type="localized" length="255" nullable="yes" category="text" description="Longer descriptive text describing a visible feature item." escapeIdtCharacters="yes" /><columnDefinition name="Display" type="number" length="2" nullable="yes" minValue="0" maxValue="32767" description="Numeric sort order, used to force a specific display ordering." /><columnDefinition name="Level" type="number" length="2" minValue="0" maxValue="32767" description="The install level at which record will be initially selected. An install level of 0 will disable an item and prevent its display." /><columnDefinition name="Directory_" type="string" length="72" nullable="yes" modularize="column" keyTable="Directory" keyColumn="1" category="upperCase" description="The name of the Directory that can be configured by the UI. A non-null value will enable the browse button." /><columnDefinition name="Attributes" type="number" length="2" set="0;1;2;4;5;6;8;9;10;16;17;18;20;21;22;24;25;26;32;33;34;36;37;38;48;49;50;52;53;54" description="Feature attributes" /></tableDefinition><tableDefinition name="FeatureComponents"><columnDefinition name="Feature_" type="string" length="38" primaryKey="yes" keyTable="Feature" keyColumn="1" category="identifier" description="Foreign key into Feature table." /><columnDefinition name="Component_" type="string" length="72" primaryKey="yes" modularize="column" keyTable="Component" keyColumn="1" category="identifier" description="Foreign key into Component table." /></tableDefinition><tableDefinition name="File" createSymbols="yes"><columnDefinition name="File" type="string" length="72" primaryKey="yes" modularize="column" category="identifier" description="Primary key, non-localized token, must match identifier in cabinet.  For uncompressed files, this field is ignored." /><columnDefinition name="Component_" type="string" length="72" modularize="column" keyTable="Component" keyColumn="1" category="identifier" description="Foreign key referencing Component that controls the file." /><columnDefinition name="FileName" type="localized" length="255" category="filename" description="File name used for installation, may be localized.  This may contain a &quot;short name|long name&quot; pair." /><columnDefinition name="FileSize" type="number" length="4" minValue="0" maxValue="2147483647" description="Size of file in bytes (long integer)." /><columnDefinition name="Version" type="string" length="72" nullable="yes" modularize="companionFile" keyTable="File" keyColumn="1" category="version" description="Version string for versioned files;  Blank for unversioned files." /><columnDefinition name="Language" type="string" length="20" nullable="yes" category="language" description="List of decimal language Ids, comma-separated if more than one." /><columnDefinition name="Attributes" type="number" length="2" nullable="yes" minValue="0" maxValue="32767" description="Integer containing bit flags representing file attributes (with the decimal value of each bit position in parentheses)" /><columnDefinition name="Sequence" type="number" length="4" minValue="1" maxValue="2147483647" description="Sequence with respect to the media images; order must track cabinet order." /></tableDefinition><tableDefinition name="InstallExecuteSequence"><columnDefinition name="Action" type="string" length="72" primaryKey="yes" category="identifier" description="Name of action to invoke, either in the engine or the handler DLL." /><columnDefinition name="Condition" type="string" length="255" nullable="yes" localizable="yes" category="condition" description="Optional expression which skips the action if evaluates to expFalse.If the expression syntax is invalid, the engine will terminate, returning iesBadActionData." /><columnDefinition name="Sequence" type="number" length="2" nullable="yes" minValue="-4" maxValue="32767" description="Number that determines the sort order in which the actions are to be executed.  Leave blank to suppress action." /></tableDefinition><tableDefinition name="InstallUISequence"><columnDefinition name="Action" type="string" length="72" primaryKey="yes" category="identifier" description="Name of action to invoke, either in the engine or the handler DLL." /><columnDefinition name="Condition" type="string" length="255" nullable="yes" localizable="yes" category="condition" description="Optional expression which skips the action if evaluates to expFalse.If the expression syntax is invalid, the engine will terminate, returning iesBadActionData." /><columnDefinition name="Sequence" type="number" length="2" nullable="yes" minValue="-4" maxValue="32767" description="Number that determines the sort order in which the actions are to be executed.  Leave blank to suppress action." /></tableDefinition><tableDefinition name="LaunchCondition"><columnDefinition name="Condition" type="string" length="255" primaryKey="yes" localizable="yes" category="condition" description="Expression which must evaluate to TRUE in order for install to commence." /><columnDefinition name="Description" type="localized" length="255" category="formatted" description="Localizable text to display when condition fails and install must abort." escapeIdtCharacters="yes" /></tableDefinition><tableDefinition name="Media" createSymbols="yes"><columnDefinition name="DiskId" type="number" length="2" primaryKey="yes" minValue="1" maxValue="32767" description="Primary key, integer to determine sort order for table." /><columnDefinition name="LastSequence" type="number" length="4" minValue="0" maxValue="2147483647" description="File sequence number for the last file for this media." /><columnDefinition name="DiskPrompt" type="localized" length="64" nullable="yes" category="text" description="Disk name: the visible text actually printed on the disk.  This will be used to prompt the user when this disk needs to be inserted." escapeIdtCharacters="yes" /><columnDefinition name="Cabinet" type="string" length="255" nullable="yes" category="cabinet" description="If some or all of the files stored on the media are compressed in a cabinet, the name of that cabinet." /><columnDefinition name="VolumeLabel" type="string" length="32" nullable="yes" category="text" description="The label attributed to the volume." /><columnDefinition name="Source" type="string" length="72" nullable="yes" category="property" description="The property defining the location of the cabinet file." /></tableDefinition><tableDefinition name="MsiFileHash"><columnDefinition name="File_" type="string" length="72" primaryKey="yes" modularize="column" keyTable="File" keyColumn="1" category="identifier" description="Primary key, foreign key into File table referencing file with this hash" /><columnDefinition name="Options" type="number" length="2" minValue="0" maxValue="32767" description="Various options and attributes for this hash." /><columnDefinition name="HashPart1" type="number" length="4" description="Size of file in bytes (long integer)." /><columnDefinition name="HashPart2" type="number" length="4" description="Size of file in bytes (long integer)." /><columnDefinition name="HashPart3" type="number" length="4" description="Size of file in bytes (long integer)." /><columnDefinition name="HashPart4" type="number" length="4" description="Size of file in bytes (long integer)." /></tableDefinition><tableDefinition name="Property" createSymbols="yes"><columnDefinition name="Property" type="string" length="72" primaryKey="yes" modularize="column" category="identifier" description="Name of property, uppercase if settable by launcher or loader." /><columnDefinition name="Value" type="localized" length="0" category="text" description="String value for property.  Never null or empty." escapeIdtCharacters="yes" /></tableDefinition><tableDefinition name="ServiceControl"><columnDefinition name="ServiceControl" type="string" length="72" primaryKey="yes" modularize="column" category="identifier" description="Primary key, non-localized token." /><columnDefinition name="Name" type="localized" length="255" modularize="property" category="formatted" description="Name of a service. /, \, comma and space are invalid" /><columnDefinition name="Event" type="number" length="2" minValue="0" maxValue="187" description="Bit field:  Install:  0x1 = Start, 0x2 = Stop, 0x8 = Delete, Uninstall: 0x10 = Start, 0x20 = Stop, 0x80 = Delete" /><columnDefinition name="Arguments" type="localized" length="255" nullable="yes" modularize="property" category="formatted" description="Arguments for the service.  Separate by [~]." /><columnDefinition name="Wait" type="number" length="2" nullable="yes" minValue="0" maxValue="1" description="Boolean for whether to wait for the service to fully start" /><columnDefinition name="Component_" type="string" length="72" modularize="column" keyTable="Component" keyColumn="1" category="identifier" description="Required foreign key into the Component Table that controls the startup of the service" /></tableDefinition><tableDefinition name="ServiceInstall"><columnDefinition name="ServiceInstall" type="string" length="72" primaryKey="yes" modularize="column" category="identifier" description="Primary key, non-localized token." /><columnDefinition name="Name" type="string" length="255" modularize="property" category="formatted" description="Internal Name of the Service" /><columnDefinition name="DisplayName" type="localized" length="255" nullable="yes" modularize="property" category="formatted" description="External Name of the Service" escapeIdtCharacters="yes" /><columnDefinition name="ServiceType" type="number" length="4" minValue="-2147483647" maxValue="2147483647" description="Type of the service" /><columnDefinition name="StartType" type="number" length="4" minValue="0" maxValue="4" description="Type of the service" /><columnDefinition name="ErrorControl" type="number" length="4" minValue="-2147483647" maxValue="2147483647" description="Severity of error if service fails to start" /><columnDefinition name="LoadOrderGroup" type="string" length="255" nullable="yes" modularize="property" category="formatted" description="LoadOrderGroup" /><columnDefinition name="Dependencies" type="string" length="255" nullable="yes" modularize="property" category="formatted" description="Other services this depends on to start.  Separate by [~], and end with [~][~]" /><columnDefinition name="StartName" type="string" length="255" nullable="yes" modularize="property" category="formatted" description="User or object name to run service as" /><columnDefinition name="Password" type="string" length="255" nullable="yes" modularize="property" category="formatted" description="password to run service with.  (with StartName)" /><columnDefinition name="Arguments" type="string" length="255" nullable="yes" modularize="property" category="formatted" description="Arguments to include in every start of the service, passed to WinMain" /><columnDefinition name="Component_" type="string" length="72" modularize="column" keyTable="Component" keyColumn="1" category="identifier" description="Required foreign key into the Component Table that controls the startup of the service" /><columnDefinition name="Description" type="localized" length="255" nullable="yes" modularize="property" category="text" description="Description of service." escapeIdtCharacters="yes" /></tableDefinition><tableDefinition name="Upgrade"><columnDefinition name="UpgradeCode" type="string" length="38" primaryKey="yes" category="guid" description="The UpgradeCode GUID belonging to the products in this set." /><columnDefinition name="VersionMin" type="string" length="20" primaryKey="yes" nullable="yes" category="text" description="The minimum ProductVersion of the products in this set.  The set may or may not include products with this particular version." /><columnDefinition name="VersionMax" type="string" length="20" primaryKey="yes" nullable="yes" category="text" description="The maximum ProductVersion of the products in this set.  The set may or may not include products with this particular version." /><columnDefinition name="Language" type="string" length="255" primaryKey="yes" nullable="yes" localizable="yes" category="language" description="A comma-separated list of languages for either products in this set or products not in this set." /><columnDefinition name="Attributes" type="number" length="4" primaryKey="yes" minValue="0" maxValue="2147483647" description="The attributes of this product set." /><columnDefinition name="Remove" type="string" length="255" nullable="yes" category="formatted" description="The list of features to remove when uninstalling a product from this set.  The default is &quot;ALL&quot;." /><columnDefinition name="ActionProperty" type="string" length="72" category="upperCase" description="The property to set when a product in this set is found." /></tableDefinition><tableDefinition name="WixAction" createSymbols="yes" unreal="yes"><columnDefinition name="SequenceTable" type="string" length="62" primaryKey="yes" /><columnDefinition name="Action" type="string" length="72" primaryKey="yes" /><columnDefinition name="Condition" type="string" length="255" nullable="yes" localizable="yes" /><columnDefinition name="Sequence" type="number" length="2" nullable="yes" /><columnDefinition name="Before" type="string" length="72" nullable="yes" /><columnDefinition name="After" type="string" length="72" nullable="yes" /><columnDefinition name="Overridable" type="number" length="2" nullable="yes" /></tableDefinition><tableDefinition name="WixBuildInfo" unreal="yes"><columnDefinition name="WixVersion" type="string" length="20" category="text" description="Version number of WiX." /><columnDefinition name="WixOutputFile" type="string" length="0" nullable="yes" category="text" description="Path to output file, if supplied." escapeIdtCharacters="yes" /><columnDefinition name="WixProjectFile" type="string" length="0" nullable="yes" category="text" description="Path to .wixproj file, if supplied." escapeIdtCharacters="yes" /><columnDefinition name="WixPdbFile" type="string" length="0" nullable="yes" category="text" description="Path to .wixpdb file, if supplied." escapeIdtCharacters="yes" /></tableDefinition><tableDefinition name="WixComplexReference" unreal="yes"><columnDefinition name="Parent" type="string" length="0" localizable="yes" /><columnDefinition name="ParentAttributes" type="number" length="4" /><columnDefinition name="ParentLanguage" type="string" length="0" nullable="yes" /><columnDefinition name="Child" type="string" length="0" localizable="yes" /><columnDefinition name="ChildAttributes" type="number" length="4" /><columnDefinition name="Attributes" type="number" length="4" /></tableDefinition><tableDefinition name="WixComponentGroup" createSymbols="yes" unreal="yes"><columnDefinition name="WixComponentGroup" type="string" length="0" primaryKey="yes" /></tableDefinition><tableDefinition name="WixFile" unreal="yes"><columnDefinition name="File_" type="string" length="0" primaryKey="yes" modularize="column" keyTable="File" keyColumn="1" /><columnDefinition name="AssemblyAttributes" type="number" length="4" nullable="yes" /><columnDefinition name="File_AssemblyManifest" type="string" length="72" nullable="yes" modularize="column" /><columnDefinition name="File_AssemblyApplication" type="string" length="72" nullable="yes" modularize="column" /><columnDefinition name="Directory_" type="string" length="72" /><columnDefinition name="DiskId" type="number" length="4" nullable="yes" /><columnDefinition name="Source" type="object" length="0" /><columnDefinition name="ProcessorArchitecture" type="string" length="0" nullable="yes" /><columnDefinition name="PatchGroup" type="number" length="4" /><columnDefinition name="Attributes" type="number" length="4" /><columnDefinition name="PatchAttributes" type="number" length="4" nullable="yes" /><columnDefinition name="RetainLengths" type="preserved" length="0" nullable="yes" category="text" /><columnDefinition name="IgnoreOffsets" type="preserved" length="0" nullable="yes" category="text" /><columnDefinition name="IgnoreLengths" type="preserved" length="0" nullable="yes" category="text" /><columnDefinition name="RetainOffsets" type="preserved" length="0" nullable="yes" category="text" /></tableDefinition><tableDefinition name="WixGroup" unreal="yes"><columnDefinition name="ParentId" type="string" length="0" primaryKey="yes" category="identifier" description="Primary key used to identify a particular record in a parent table." /><columnDefinition name="ParentType" type="string" length="0" primaryKey="yes" description="Primary key used to identify a particular parent type in a parent table." /><columnDefinition name="ChildId" type="string" length="0" primaryKey="yes" category="identifier" description="Primary key used to identify a particular record in a child table." /><columnDefinition name="ChildType" type="string" length="0" primaryKey="yes" description="Primary key used to identify a particular child type in a child table." /></tableDefinition><tableDefinition name="WixSimpleReference" unreal="yes"><columnDefinition name="Table" type="string" length="32" /><columnDefinition name="PrimaryKeys" type="string" length="0" /></tableDefinition></tableDefinitions><table name="_Streams" xmlns="http://schemas.microsoft.com/wix/2006/objects" /><table name="_SummaryInformation" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*12"><field>1</field><field>1252</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*12"><field>2</field><field>Installation Database</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*12"><field>3</field><field>LSB Telemetry Service</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*12"><field>4</field><field>LSB</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*12"><field>5</field><field>Installer</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*12"><field>6</field><field>This installer database contains the logic and data required to install LSB Telemetry Service.</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*12"><field>7</field><field>x64;1033</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*12"><field>9</field><field>{5AA7CED3-628B-490B-B4CE-ECA0FCA95D9D}</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*12"><field>14</field><field>200</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*12"><field>15</field><field>2</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*12"><field>19</field><field>2</field></row><row><field>12</field><field>2025/06/03 00:30:56</field></row><row><field>13</field><field>2025/06/03 00:30:56</field></row><row><field>18</field><field>Windows Installer XML Toolset (3.14.1.8722)</field></row></table><table name="_Validation" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row><field>_SummaryInformation</field><field>PropertyId</field><field>N</field><field /><field /><field /><field /><field /><field /><field /></row><row><field>_SummaryInformation</field><field>Value</field><field>N</field><field /><field /><field /><field /><field /><field /><field /></row><row><field>_Validation</field><field>Table</field><field>N</field><field /><field /><field /><field /><field>Identifier</field><field /><field>Name of table</field></row><row><field>_Validation</field><field>Column</field><field>N</field><field /><field /><field /><field /><field>Identifier</field><field /><field>Name of column</field></row><row><field>_Validation</field><field>Nullable</field><field>N</field><field /><field /><field /><field /><field /><field>Y;N</field><field>Whether the column is nullable</field></row><row><field>_Validation</field><field>MinValue</field><field>Y</field><field>-2147483647</field><field>2147483647</field><field /><field /><field /><field /><field>Minimum value allowed</field></row><row><field>_Validation</field><field>MaxValue</field><field>Y</field><field>-2147483647</field><field>2147483647</field><field /><field /><field /><field /><field>Maximum value allowed</field></row><row><field>_Validation</field><field>KeyTable</field><field>Y</field><field /><field /><field /><field /><field>Identifier</field><field /><field>For foreign key, Name of table to which data must link</field></row><row><field>_Validation</field><field>KeyColumn</field><field>Y</field><field>1</field><field>32</field><field /><field /><field /><field /><field>Column to which foreign key connects</field></row><row><field>_Validation</field><field>Category</field><field>Y</field><field /><field /><field /><field /><field /><field>Text;Formatted;Template;Condition;Guid;Path;Version;Language;Identifier;Binary;UpperCase;LowerCase;Filename;Paths;AnyPath;WildCardFilename;RegPath;CustomSource;Property;Cabinet;Shortcut;FormattedSDDLText;Integer;DoubleInteger;TimeDate;DefaultDir</field><field>String category</field></row><row><field>_Validation</field><field>Set</field><field>Y</field><field /><field /><field /><field /><field>Text</field><field /><field>Set of values that are permitted</field></row><row><field>_Validation</field><field>Description</field><field>Y</field><field /><field /><field /><field /><field>Text</field><field /><field>Description of column</field></row><row><field>AdminExecuteSequence</field><field>Action</field><field>N</field><field /><field /><field /><field /><field>Identifier</field><field /><field>Name of action to invoke, either in the engine or the handler DLL.</field></row><row><field>AdminExecuteSequence</field><field>Condition</field><field>Y</field><field /><field /><field /><field /><field>Condition</field><field /><field>Optional expression which skips the action if evaluates to expFalse.If the expression syntax is invalid, the engine will terminate, returning iesBadActionData.</field></row><row><field>AdminExecuteSequence</field><field>Sequence</field><field>Y</field><field>-4</field><field>32767</field><field /><field /><field /><field /><field>Number that determines the sort order in which the actions are to be executed.  Leave blank to suppress action.</field></row><row><field>AdminUISequence</field><field>Action</field><field>N</field><field /><field /><field /><field /><field>Identifier</field><field /><field>Name of action to invoke, either in the engine or the handler DLL.</field></row><row><field>AdminUISequence</field><field>Condition</field><field>Y</field><field /><field /><field /><field /><field>Condition</field><field /><field>Optional expression which skips the action if evaluates to expFalse.If the expression syntax is invalid, the engine will terminate, returning iesBadActionData.</field></row><row><field>AdminUISequence</field><field>Sequence</field><field>Y</field><field>-4</field><field>32767</field><field /><field /><field /><field /><field>Number that determines the sort order in which the actions are to be executed.  Leave blank to suppress action.</field></row><row><field>AdvtExecuteSequence</field><field>Action</field><field>N</field><field /><field /><field /><field /><field>Identifier</field><field /><field>Name of action to invoke, either in the engine or the handler DLL.</field></row><row><field>AdvtExecuteSequence</field><field>Condition</field><field>Y</field><field /><field /><field /><field /><field>Condition</field><field /><field>Optional expression which skips the action if evaluates to expFalse.If the expression syntax is invalid, the engine will terminate, returning iesBadActionData.</field></row><row><field>AdvtExecuteSequence</field><field>Sequence</field><field>Y</field><field>-4</field><field>32767</field><field /><field /><field /><field /><field>Number that determines the sort order in which the actions are to be executed.  Leave blank to suppress action.</field></row><row><field>Component</field><field>Component</field><field>N</field><field /><field /><field /><field /><field>Identifier</field><field /><field>Primary key used to identify a particular component record.</field></row><row><field>Component</field><field>ComponentId</field><field>Y</field><field /><field /><field /><field /><field>Guid</field><field /><field>A string GUID unique to this component, version, and language.</field></row><row><field>Component</field><field>Directory_</field><field>N</field><field /><field /><field>Directory</field><field>1</field><field>Identifier</field><field /><field>Required key of a Directory table record. This is actually a property name whose value contains the actual path, set either by the AppSearch action or with the default setting obtained from the Directory table.</field></row><row><field>Component</field><field>Attributes</field><field>N</field><field /><field /><field /><field /><field /><field /><field>Remote execution option, one of irsEnum</field></row><row><field>Component</field><field>Condition</field><field>Y</field><field /><field /><field /><field /><field>Condition</field><field /><field>A conditional statement that will disable this component if the specified condition evaluates to the 'True' state. If a component is disabled, it will not be installed, regardless of the 'Action' state associated with the component.</field></row><row><field>Component</field><field>KeyPath</field><field>Y</field><field /><field /><field>File;Registry;ODBCDataSource</field><field>1</field><field>Identifier</field><field /><field>Either the primary key into the File table, Registry table, or ODBCDataSource table. This extract path is stored when the component is installed, and is used to detect the presence of the component and to return the path to it.</field></row><row><field>CustomAction</field><field>Action</field><field>N</field><field /><field /><field /><field /><field>Identifier</field><field /><field>Primary key, name of action, normally appears in sequence table unless private use.</field></row><row><field>CustomAction</field><field>Type</field><field>N</field><field>1</field><field>32767</field><field /><field /><field /><field /><field>The numeric custom action type, consisting of source location, code type, entry, option flags.</field></row><row><field>CustomAction</field><field>Source</field><field>Y</field><field /><field /><field /><field /><field>CustomSource</field><field /><field>The table reference of the source of the code.</field></row><row><field>CustomAction</field><field>Target</field><field>Y</field><field /><field /><field /><field /><field>Formatted</field><field /><field>Excecution parameter, depends on the type of custom action</field></row><row><field>CustomAction</field><field>ExtendedType</field><field>Y</field><field>0</field><field>2147483647</field><field /><field /><field /><field /><field>A numeric custom action type that extends code type or option flags of the Type column.</field></row><row><field>Directory</field><field>Directory</field><field>N</field><field /><field /><field /><field /><field>Identifier</field><field /><field>Unique identifier for directory entry, primary key. If a property by this name is defined, it contains the full path to the directory.</field></row><row><field>Directory</field><field>Directory_Parent</field><field>Y</field><field /><field /><field>Directory</field><field>1</field><field>Identifier</field><field /><field>Reference to the entry in this table specifying the default parent directory. A record parented to itself or with a Null parent represents a root of the install tree.</field></row><row><field>Directory</field><field>DefaultDir</field><field>N</field><field /><field /><field /><field /><field>DefaultDir</field><field /><field>The default sub-path under parent's path.</field></row><row><field>Feature</field><field>Feature</field><field>N</field><field /><field /><field /><field /><field>Identifier</field><field /><field>Primary key used to identify a particular feature record.</field></row><row><field>Feature</field><field>Feature_Parent</field><field>Y</field><field /><field /><field>Feature</field><field>1</field><field>Identifier</field><field /><field>Optional key of a parent record in the same table. If the parent is not selected, then the record will not be installed. Null indicates a root item.</field></row><row><field>Feature</field><field>Title</field><field>Y</field><field /><field /><field /><field /><field>Text</field><field /><field>Short text identifying a visible feature item.</field></row><row><field>Feature</field><field>Description</field><field>Y</field><field /><field /><field /><field /><field>Text</field><field /><field>Longer descriptive text describing a visible feature item.</field></row><row><field>Feature</field><field>Display</field><field>Y</field><field>0</field><field>32767</field><field /><field /><field /><field /><field>Numeric sort order, used to force a specific display ordering.</field></row><row><field>Feature</field><field>Level</field><field>N</field><field>0</field><field>32767</field><field /><field /><field /><field /><field>The install level at which record will be initially selected. An install level of 0 will disable an item and prevent its display.</field></row><row><field>Feature</field><field>Directory_</field><field>Y</field><field /><field /><field>Directory</field><field>1</field><field>UpperCase</field><field /><field>The name of the Directory that can be configured by the UI. A non-null value will enable the browse button.</field></row><row><field>Feature</field><field>Attributes</field><field>N</field><field /><field /><field /><field /><field /><field>0;1;2;4;5;6;8;9;10;16;17;18;20;21;22;24;25;26;32;33;34;36;37;38;48;49;50;52;53;54</field><field>Feature attributes</field></row><row><field>FeatureComponents</field><field>Feature_</field><field>N</field><field /><field /><field>Feature</field><field>1</field><field>Identifier</field><field /><field>Foreign key into Feature table.</field></row><row><field>FeatureComponents</field><field>Component_</field><field>N</field><field /><field /><field>Component</field><field>1</field><field>Identifier</field><field /><field>Foreign key into Component table.</field></row><row><field>File</field><field>File</field><field>N</field><field /><field /><field /><field /><field>Identifier</field><field /><field>Primary key, non-localized token, must match identifier in cabinet.  For uncompressed files, this field is ignored.</field></row><row><field>File</field><field>Component_</field><field>N</field><field /><field /><field>Component</field><field>1</field><field>Identifier</field><field /><field>Foreign key referencing Component that controls the file.</field></row><row><field>File</field><field>FileName</field><field>N</field><field /><field /><field /><field /><field>Filename</field><field /><field>File name used for installation, may be localized.  This may contain a "short name|long name" pair.</field></row><row><field>File</field><field>FileSize</field><field>N</field><field>0</field><field>2147483647</field><field /><field /><field /><field /><field>Size of file in bytes (long integer).</field></row><row><field>File</field><field>Version</field><field>Y</field><field /><field /><field>File</field><field>1</field><field>Version</field><field /><field>Version string for versioned files;  Blank for unversioned files.</field></row><row><field>File</field><field>Language</field><field>Y</field><field /><field /><field /><field /><field>Language</field><field /><field>List of decimal language Ids, comma-separated if more than one.</field></row><row><field>File</field><field>Attributes</field><field>Y</field><field>0</field><field>32767</field><field /><field /><field /><field /><field>Integer containing bit flags representing file attributes (with the decimal value of each bit position in parentheses)</field></row><row><field>File</field><field>Sequence</field><field>N</field><field>1</field><field>2147483647</field><field /><field /><field /><field /><field>Sequence with respect to the media images; order must track cabinet order.</field></row><row><field>InstallExecuteSequence</field><field>Action</field><field>N</field><field /><field /><field /><field /><field>Identifier</field><field /><field>Name of action to invoke, either in the engine or the handler DLL.</field></row><row><field>InstallExecuteSequence</field><field>Condition</field><field>Y</field><field /><field /><field /><field /><field>Condition</field><field /><field>Optional expression which skips the action if evaluates to expFalse.If the expression syntax is invalid, the engine will terminate, returning iesBadActionData.</field></row><row><field>InstallExecuteSequence</field><field>Sequence</field><field>Y</field><field>-4</field><field>32767</field><field /><field /><field /><field /><field>Number that determines the sort order in which the actions are to be executed.  Leave blank to suppress action.</field></row><row><field>InstallUISequence</field><field>Action</field><field>N</field><field /><field /><field /><field /><field>Identifier</field><field /><field>Name of action to invoke, either in the engine or the handler DLL.</field></row><row><field>InstallUISequence</field><field>Condition</field><field>Y</field><field /><field /><field /><field /><field>Condition</field><field /><field>Optional expression which skips the action if evaluates to expFalse.If the expression syntax is invalid, the engine will terminate, returning iesBadActionData.</field></row><row><field>InstallUISequence</field><field>Sequence</field><field>Y</field><field>-4</field><field>32767</field><field /><field /><field /><field /><field>Number that determines the sort order in which the actions are to be executed.  Leave blank to suppress action.</field></row><row><field>LaunchCondition</field><field>Condition</field><field>N</field><field /><field /><field /><field /><field>Condition</field><field /><field>Expression which must evaluate to TRUE in order for install to commence.</field></row><row><field>LaunchCondition</field><field>Description</field><field>N</field><field /><field /><field /><field /><field>Formatted</field><field /><field>Localizable text to display when condition fails and install must abort.</field></row><row><field>Media</field><field>DiskId</field><field>N</field><field>1</field><field>32767</field><field /><field /><field /><field /><field>Primary key, integer to determine sort order for table.</field></row><row><field>Media</field><field>LastSequence</field><field>N</field><field>0</field><field>2147483647</field><field /><field /><field /><field /><field>File sequence number for the last file for this media.</field></row><row><field>Media</field><field>DiskPrompt</field><field>Y</field><field /><field /><field /><field /><field>Text</field><field /><field>Disk name: the visible text actually printed on the disk.  This will be used to prompt the user when this disk needs to be inserted.</field></row><row><field>Media</field><field>Cabinet</field><field>Y</field><field /><field /><field /><field /><field>Cabinet</field><field /><field>If some or all of the files stored on the media are compressed in a cabinet, the name of that cabinet.</field></row><row><field>Media</field><field>VolumeLabel</field><field>Y</field><field /><field /><field /><field /><field>Text</field><field /><field>The label attributed to the volume.</field></row><row><field>Media</field><field>Source</field><field>Y</field><field /><field /><field /><field /><field>Property</field><field /><field>The property defining the location of the cabinet file.</field></row><row><field>MsiFileHash</field><field>File_</field><field>N</field><field /><field /><field>File</field><field>1</field><field>Identifier</field><field /><field>Primary key, foreign key into File table referencing file with this hash</field></row><row><field>MsiFileHash</field><field>Options</field><field>N</field><field>0</field><field>32767</field><field /><field /><field /><field /><field>Various options and attributes for this hash.</field></row><row><field>MsiFileHash</field><field>HashPart1</field><field>N</field><field /><field /><field /><field /><field /><field /><field>Size of file in bytes (long integer).</field></row><row><field>MsiFileHash</field><field>HashPart2</field><field>N</field><field /><field /><field /><field /><field /><field /><field>Size of file in bytes (long integer).</field></row><row><field>MsiFileHash</field><field>HashPart3</field><field>N</field><field /><field /><field /><field /><field /><field /><field>Size of file in bytes (long integer).</field></row><row><field>MsiFileHash</field><field>HashPart4</field><field>N</field><field /><field /><field /><field /><field /><field /><field>Size of file in bytes (long integer).</field></row><row><field>Property</field><field>Property</field><field>N</field><field /><field /><field /><field /><field>Identifier</field><field /><field>Name of property, uppercase if settable by launcher or loader.</field></row><row><field>Property</field><field>Value</field><field>N</field><field /><field /><field /><field /><field>Text</field><field /><field>String value for property.  Never null or empty.</field></row><row><field>ServiceControl</field><field>ServiceControl</field><field>N</field><field /><field /><field /><field /><field>Identifier</field><field /><field>Primary key, non-localized token.</field></row><row><field>ServiceControl</field><field>Name</field><field>N</field><field /><field /><field /><field /><field>Formatted</field><field /><field>Name of a service. /, \, comma and space are invalid</field></row><row><field>ServiceControl</field><field>Event</field><field>N</field><field>0</field><field>187</field><field /><field /><field /><field /><field>Bit field:  Install:  0x1 = Start, 0x2 = Stop, 0x8 = Delete, Uninstall: 0x10 = Start, 0x20 = Stop, 0x80 = Delete</field></row><row><field>ServiceControl</field><field>Arguments</field><field>Y</field><field /><field /><field /><field /><field>Formatted</field><field /><field>Arguments for the service.  Separate by [~].</field></row><row><field>ServiceControl</field><field>Wait</field><field>Y</field><field>0</field><field>1</field><field /><field /><field /><field /><field>Boolean for whether to wait for the service to fully start</field></row><row><field>ServiceControl</field><field>Component_</field><field>N</field><field /><field /><field>Component</field><field>1</field><field>Identifier</field><field /><field>Required foreign key into the Component Table that controls the startup of the service</field></row><row><field>ServiceInstall</field><field>ServiceInstall</field><field>N</field><field /><field /><field /><field /><field>Identifier</field><field /><field>Primary key, non-localized token.</field></row><row><field>ServiceInstall</field><field>Name</field><field>N</field><field /><field /><field /><field /><field>Formatted</field><field /><field>Internal Name of the Service</field></row><row><field>ServiceInstall</field><field>DisplayName</field><field>Y</field><field /><field /><field /><field /><field>Formatted</field><field /><field>External Name of the Service</field></row><row><field>ServiceInstall</field><field>ServiceType</field><field>N</field><field>-2147483647</field><field>2147483647</field><field /><field /><field /><field /><field>Type of the service</field></row><row><field>ServiceInstall</field><field>StartType</field><field>N</field><field>0</field><field>4</field><field /><field /><field /><field /><field>Type of the service</field></row><row><field>ServiceInstall</field><field>ErrorControl</field><field>N</field><field>-2147483647</field><field>2147483647</field><field /><field /><field /><field /><field>Severity of error if service fails to start</field></row><row><field>ServiceInstall</field><field>LoadOrderGroup</field><field>Y</field><field /><field /><field /><field /><field>Formatted</field><field /><field>LoadOrderGroup</field></row><row><field>ServiceInstall</field><field>Dependencies</field><field>Y</field><field /><field /><field /><field /><field>Formatted</field><field /><field>Other services this depends on to start.  Separate by [~], and end with [~][~]</field></row><row><field>ServiceInstall</field><field>StartName</field><field>Y</field><field /><field /><field /><field /><field>Formatted</field><field /><field>User or object name to run service as</field></row><row><field>ServiceInstall</field><field>Password</field><field>Y</field><field /><field /><field /><field /><field>Formatted</field><field /><field>password to run service with.  (with StartName)</field></row><row><field>ServiceInstall</field><field>Arguments</field><field>Y</field><field /><field /><field /><field /><field>Formatted</field><field /><field>Arguments to include in every start of the service, passed to WinMain</field></row><row><field>ServiceInstall</field><field>Component_</field><field>N</field><field /><field /><field>Component</field><field>1</field><field>Identifier</field><field /><field>Required foreign key into the Component Table that controls the startup of the service</field></row><row><field>ServiceInstall</field><field>Description</field><field>Y</field><field /><field /><field /><field /><field>Text</field><field /><field>Description of service.</field></row><row><field>Upgrade</field><field>UpgradeCode</field><field>N</field><field /><field /><field /><field /><field>Guid</field><field /><field>The UpgradeCode GUID belonging to the products in this set.</field></row><row><field>Upgrade</field><field>VersionMin</field><field>Y</field><field /><field /><field /><field /><field>Text</field><field /><field>The minimum ProductVersion of the products in this set.  The set may or may not include products with this particular version.</field></row><row><field>Upgrade</field><field>VersionMax</field><field>Y</field><field /><field /><field /><field /><field>Text</field><field /><field>The maximum ProductVersion of the products in this set.  The set may or may not include products with this particular version.</field></row><row><field>Upgrade</field><field>Language</field><field>Y</field><field /><field /><field /><field /><field>Language</field><field /><field>A comma-separated list of languages for either products in this set or products not in this set.</field></row><row><field>Upgrade</field><field>Attributes</field><field>N</field><field>0</field><field>2147483647</field><field /><field /><field /><field /><field>The attributes of this product set.</field></row><row><field>Upgrade</field><field>Remove</field><field>Y</field><field /><field /><field /><field /><field>Formatted</field><field /><field>The list of features to remove when uninstalling a product from this set.  The default is "ALL".</field></row><row><field>Upgrade</field><field>ActionProperty</field><field>N</field><field /><field /><field /><field /><field>UpperCase</field><field /><field>The property to set when a product in this set is found.</field></row></table><table name="AdminExecuteSequence" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row><field>CostInitialize</field><field /><field>800</field></row><row><field>FileCost</field><field /><field>900</field></row><row><field>CostFinalize</field><field /><field>1000</field></row><row><field>InstallValidate</field><field /><field>1400</field></row><row><field>InstallInitialize</field><field /><field>1500</field></row><row><field>InstallAdminPackage</field><field /><field>3900</field></row><row><field>InstallFiles</field><field /><field>4000</field></row><row><field>InstallFinalize</field><field /><field>6600</field></row></table><table name="AdminUISequence" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row><field>CostInitialize</field><field /><field>800</field></row><row><field>FileCost</field><field /><field>900</field></row><row><field>CostFinalize</field><field /><field>1000</field></row><row><field>ExecuteAction</field><field /><field>1300</field></row></table><table name="AdvtExecuteSequence" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row><field>CostInitialize</field><field /><field>800</field></row><row><field>CostFinalize</field><field /><field>1000</field></row><row><field>InstallValidate</field><field /><field>1400</field></row><row><field>InstallInitialize</field><field /><field>1500</field></row><row><field>PublishFeatures</field><field /><field>6300</field></row><row><field>PublishProduct</field><field /><field>6400</field></row><row><field>InstallFinalize</field><field /><field>6600</field></row></table><table name="Component" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*64"><field>ServiceExe</field><field>{8CE3D205-31EE-415D-81C5-57FEDEB4B6A0}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>ServiceExe</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*86"><field>LSBLogMonitorServicedepsjson</field><field>{9A659D45-9AF3-4B15-BABD-5104DCB9C0F1}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>LSBLogMonitorServicedepsjson</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*90"><field>LSBLogMonitorServiceruntimeconfigjson</field><field>{3D9F0C37-928E-49F1-A95D-EC165D776FF2}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>LSBLogMonitorServiceruntimeconfigjson</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*94"><field>LSBLogMonitorServicepdb</field><field>{525894B9-440A-4F7F-B4E6-A21816A22AEE}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>LSBLogMonitorServicepdb</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*98"><field>appsettingsjson</field><field>{848F0633-58EF-40D6-AB4E-A41380B24E20}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>appsettingsjson</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*102"><field>appsettingsDevelopmentjson</field><field>{AFC38A06-16C6-4D0A-91C9-AE7B0BB2C883}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>appsettingsDevelopmentjson</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*106"><field>LSBLogMonitorServicedll</field><field>{68E1223C-A55B-4A86-9857-C2C19F16DC9A}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>LSBLogMonitorServicedll</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*110"><field>MicrosoftExtensionsConfigurationAbstractionsdll</field><field>{0E818EDD-A484-4265-8BF3-BBBEC74472C5}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsConfigurationAbstractionsdll</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*114"><field>MicrosoftExtensionsConfigurationBinderdll</field><field>{F3B862F2-5736-4FD0-A1F1-00D0185A2CD1}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsConfigurationBinderdll</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*118"><field>MicrosoftExtensionsConfigurationCommandLinedll</field><field>{341A63AC-E3D7-4EEA-A6FE-B3D7420E822E}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsConfigurationCommandLinedll</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*122"><field>MicrosoftExtensionsConfigurationdll</field><field>{86F32E48-0C81-4862-954C-C8339E445BAA}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsConfigurationdll</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*126"><field>MicrosoftExtensionsConfigurationEnvironmentVariablesdll</field><field>{E25D2ABE-6159-48F0-A19F-98B37A30CBF1}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsConfigurationEnvironmentVariablesdll</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*130"><field>MicrosoftExtensionsConfigurationFileExtensionsdll</field><field>{23678AB0-BAE8-4A97-AA87-6176276BE472}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsConfigurationFileExtensionsdll</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*134"><field>MicrosoftExtensionsConfigurationJsondll</field><field>{8B2CAD49-BB44-4D68-9D01-467AA5AF6924}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsConfigurationJsondll</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*138"><field>MicrosoftExtensionsConfigurationUserSecretsdll</field><field>{1B8EDBCE-32AE-479C-9B1C-ABEA446DE35A}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsConfigurationUserSecretsdll</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*142"><field>MicrosoftExtensionsDependencyInjectionAbstractionsdll</field><field>{F1F3EF1B-B361-4438-975E-B2CFF01D0C7A}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsDependencyInjectionAbstractionsdll</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*146"><field>MicrosoftExtensionsDependencyInjectiondll</field><field>{E8AEEE64-EC22-4469-9EEC-07176FE02D44}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsDependencyInjectiondll</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*150"><field>MicrosoftExtensionsDiagnosticsAbstractionsdll</field><field>{D140B3AE-E543-4542-A782-4B30D5000A63}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsDiagnosticsAbstractionsdll</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*154"><field>MicrosoftExtensionsDiagnosticsdll</field><field>{5DFF7A20-228D-41D2-8E89-1F038E8AC67D}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsDiagnosticsdll</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*158"><field>MicrosoftExtensionsFileProvidersAbstractionsdll</field><field>{DE6001D9-D751-436A-9059-1B5C1FE39728}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsFileProvidersAbstractionsdll</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*162"><field>MicrosoftExtensionsFileProvidersPhysicaldll</field><field>{D9E437EE-38C9-4977-AED6-7BC95C56B28E}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsFileProvidersPhysicaldll</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*166"><field>MicrosoftExtensionsFileSystemGlobbingdll</field><field>{8013191F-6A56-4C9D-B967-6353E672207F}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsFileSystemGlobbingdll</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*170"><field>MicrosoftExtensionsHostingAbstractionsdll</field><field>{3EA3AACF-6060-45BE-B2ED-9BCD396CEB09}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsHostingAbstractionsdll</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*174"><field>MicrosoftExtensionsHostingdll</field><field>{9471016F-65CC-438B-9FC6-BB6BE965320C}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsHostingdll</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*178"><field>MicrosoftExtensionsHostingWindowsServicesdll</field><field>{EAB556B6-E086-4EF8-92DE-138BA836DC6F}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsHostingWindowsServicesdll</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*182"><field>MicrosoftExtensionsHttpdll</field><field>{********-106D-4A88-993F-2E7BEECE19B3}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsHttpdll</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*186"><field>MicrosoftExtensionsLoggingAbstractionsdll</field><field>{A46B2EB9-3E0F-4B8F-BD18-4D10C976A012}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsLoggingAbstractionsdll</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*190"><field>MicrosoftExtensionsLoggingConfigurationdll</field><field>{6C4C943A-6DCE-429F-91E6-4BBDCF718180}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsLoggingConfigurationdll</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*194"><field>MicrosoftExtensionsLoggingConsoledll</field><field>{0826C5CE-3AA2-484E-8396-4EB616790E49}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsLoggingConsoledll</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*198"><field>MicrosoftExtensionsLoggingDebugdll</field><field>{D54B24B5-E152-4EC9-AE03-8B88F7685854}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsLoggingDebugdll</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*202"><field>MicrosoftExtensionsLoggingdll</field><field>{C554B085-9CEC-43F9-A26F-59E608B5FABB}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsLoggingdll</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*206"><field>MicrosoftExtensionsLoggingEventLogdll</field><field>{DFC5F360-C117-470B-AAF6-971646CDB696}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsLoggingEventLogdll</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*210"><field>MicrosoftExtensionsLoggingEventSourcedll</field><field>{696797FA-4B15-41F1-AA8D-03FC19E9EE77}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsLoggingEventSourcedll</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*214"><field>MicrosoftExtensionsOptionsConfigurationExtensionsdll</field><field>{0D68C4CB-1B4C-48A4-BA71-1D9C0010304B}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsOptionsConfigurationExtensionsdll</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*218"><field>MicrosoftExtensionsOptionsdll</field><field>{ABF5D288-B8C2-4F1E-B9FE-6BAE80DD1306}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsOptionsdll</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*222"><field>MicrosoftExtensionsPrimitivesdll</field><field>{CEFFAA3D-91D2-46FF-B2E9-F82E0F573902}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>MicrosoftExtensionsPrimitivesdll</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*226"><field>SystemCodeDomdll</field><field>{12788CB1-D97E-498A-B776-53C4FCF85310}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>SystemCodeDomdll</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*230"><field>SystemConfigurationConfigurationManagerdll</field><field>{572AB4F2-8F70-4717-AC9D-FCD1BE2F00A5}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>SystemConfigurationConfigurationManagerdll</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*234"><field>SystemDiagnosticsEventLogdll</field><field>{771DB305-AB86-44C7-B7BC-CC77E5F91CEF}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>SystemDiagnosticsEventLogdll</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*238"><field>SystemDiagnosticsPerformanceCounterdll</field><field>{E04EB193-F442-4A81-83F3-6F470C079C99}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>SystemDiagnosticsPerformanceCounterdll</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*242"><field>SystemIOPipelinesdll</field><field>{********-B415-4642-B670-5EB30A28FD8D}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>SystemIOPipelinesdll</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*246"><field>SystemManagementdll</field><field>{37C1F26E-A849-45C6-9F5B-574C6FEA4CED}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>SystemManagementdll</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*250"><field>SystemSecurityCryptographyProtectedDatadll</field><field>{5B8284FC-9229-44E2-A641-08CF3873C8E2}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>SystemSecurityCryptographyProtectedDatadll</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*254"><field>SystemServiceProcessServiceControllerdll</field><field>{BFD5A2BE-E07B-4711-A874-5A34CD247185}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>SystemServiceProcessServiceControllerdll</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*258"><field>SystemTextEncodingsWebdll</field><field>{F54583A1-9EAB-4185-BCB7-67DBECB619C8}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>SystemTextEncodingsWebdll</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*262"><field>SystemTextJsondll</field><field>{432C4CB4-A4AE-4AA0-A704-A4B707D99682}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>SystemTextJsondll</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*266"><field>YamlDotNetdll</field><field>{FD9C936E-E941-4500-A48F-A75C071807AD}</field><field>INSTALLFOLDER</field><field>256</field><field /><field>YamlDotNetdll</field></row></table><table name="CustomAction" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*35"><field>SetServiceTimeout</field><field>3106</field><field>INSTALLFOLDER</field><field>reg add "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control" /v "ServicesPipeTimeout" /t REG_DWORD /d "180000" /f</field><field /></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*41"><field>SetServiceConfig</field><field>3106</field><field>INSTALLFOLDER</field><field>sc config LSBTelemetryService start= auto obj= LocalSystem</field><field /></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*47"><field>SetServiceFailureActions</field><field>3106</field><field>INSTALLFOLDER</field><field>sc failure LSBTelemetryService reset= 86400 actions= restart/30000/restart/60000/restart/60000</field><field /></row></table><table name="Directory" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*25"><field>INSTALLFOLDER</field><field>CompanyFolder</field><field>2u63yf5o|TelemetryService</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*24"><field>CompanyFolder</field><field>ProgramFiles64Folder</field><field>LSB</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*23"><field>ProgramFiles64Folder</field><field>TARGETDIR</field><field>.</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*22"><field>TARGETDIR</field><field /><field>SourceDir</field></row></table><table name="Feature" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*30"><field>Complete</field><field /><field /><field /><field>2</field><field>1</field><field /><field>0</field></row></table><table name="FeatureComponents" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row sectionId="wix.section.8"><field>Complete</field><field>LSBLogMonitorServicedepsjson</field></row><row sectionId="wix.section.8"><field>Complete</field><field>LSBLogMonitorServicedll</field></row><row sectionId="wix.section.8"><field>Complete</field><field>LSBLogMonitorServicepdb</field></row><row sectionId="wix.section.8"><field>Complete</field><field>LSBLogMonitorServiceruntimeconfigjson</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsConfigurationAbstractionsdll</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsConfigurationBinderdll</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsConfigurationCommandLinedll</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsConfigurationEnvironmentVariablesdll</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsConfigurationFileExtensionsdll</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsConfigurationJsondll</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsConfigurationUserSecretsdll</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsConfigurationdll</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsDependencyInjectionAbstractionsdll</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsDependencyInjectiondll</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsDiagnosticsAbstractionsdll</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsDiagnosticsdll</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsFileProvidersAbstractionsdll</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsFileProvidersPhysicaldll</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsFileSystemGlobbingdll</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsHostingAbstractionsdll</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsHostingWindowsServicesdll</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsHostingdll</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsHttpdll</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsLoggingAbstractionsdll</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsLoggingConfigurationdll</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsLoggingConsoledll</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsLoggingDebugdll</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsLoggingEventLogdll</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsLoggingEventSourcedll</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsLoggingdll</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsOptionsConfigurationExtensionsdll</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsOptionsdll</field></row><row sectionId="wix.section.8"><field>Complete</field><field>MicrosoftExtensionsPrimitivesdll</field></row><row sectionId="wix.section.8"><field>Complete</field><field>ServiceExe</field></row><row sectionId="wix.section.8"><field>Complete</field><field>SystemCodeDomdll</field></row><row sectionId="wix.section.8"><field>Complete</field><field>SystemConfigurationConfigurationManagerdll</field></row><row sectionId="wix.section.8"><field>Complete</field><field>SystemDiagnosticsEventLogdll</field></row><row sectionId="wix.section.8"><field>Complete</field><field>SystemDiagnosticsPerformanceCounterdll</field></row><row sectionId="wix.section.8"><field>Complete</field><field>SystemIOPipelinesdll</field></row><row sectionId="wix.section.8"><field>Complete</field><field>SystemManagementdll</field></row><row sectionId="wix.section.8"><field>Complete</field><field>SystemSecurityCryptographyProtectedDatadll</field></row><row sectionId="wix.section.8"><field>Complete</field><field>SystemServiceProcessServiceControllerdll</field></row><row sectionId="wix.section.8"><field>Complete</field><field>SystemTextEncodingsWebdll</field></row><row sectionId="wix.section.8"><field>Complete</field><field>SystemTextJsondll</field></row><row sectionId="wix.section.8"><field>Complete</field><field>YamlDotNetdll</field></row><row sectionId="wix.section.8"><field>Complete</field><field>appsettingsDevelopmentjson</field></row><row sectionId="wix.section.8"><field>Complete</field><field>appsettingsjson</field></row></table><table name="File" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*65"><field>ServiceExe</field><field>ServiceExe</field><field>_1h8sy4e.exe|LSB.LogMonitor.Service.exe</field><field>139264</field><field>1.0.0.0</field><field>0</field><field>512</field><field>36</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*87"><field>LSBLogMonitorServicedepsjson</field><field>LSBLogMonitorServicedepsjson</field><field>-q4tkbqt.jso|LSB.LogMonitor.Service.deps.json</field><field>36230</field><field /><field /><field>512</field><field>3</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*91"><field>LSBLogMonitorServiceruntimeconfigjson</field><field>LSBLogMonitorServiceruntimeconfigjson</field><field>wqw6mnad.jso|LSB.LogMonitor.Service.runtimeconfig.json</field><field>340</field><field /><field /><field>512</field><field>6</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*95"><field>LSBLogMonitorServicepdb</field><field>LSBLogMonitorServicepdb</field><field>hlqbc1z5.pdb|LSB.LogMonitor.Service.pdb</field><field>35532</field><field /><field /><field>512</field><field>5</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*99"><field>appsettingsjson</field><field>appsettingsjson</field><field>parw1tbt.jso|appsettings.json</field><field>746</field><field /><field /><field>512</field><field>2</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*103"><field>appsettingsDevelopmentjson</field><field>appsettingsDevelopmentjson</field><field>xymp1_g-.jso|appsettings.Development.json</field><field>137</field><field /><field /><field>512</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*107"><field>LSBLogMonitorServicedll</field><field>LSBLogMonitorServicedll</field><field>kjw-mt-i.dll|LSB.LogMonitor.Service.dll</field><field>114688</field><field>1.0.0.0</field><field>0</field><field>512</field><field>4</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*111"><field>MicrosoftExtensionsConfigurationAbstractionsdll</field><field>MicrosoftExtensionsConfigurationAbstractionsdll</field><field>xgzi7s7d.dll|Microsoft.Extensions.Configuration.Abstractions.dll</field><field>27936</field><field>8.0.23.53103</field><field>0</field><field>512</field><field>7</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*115"><field>MicrosoftExtensionsConfigurationBinderdll</field><field>MicrosoftExtensionsConfigurationBinderdll</field><field>7fu0wmnp.dll|Microsoft.Extensions.Configuration.Binder.dll</field><field>42784</field><field>8.0.724.31311</field><field>0</field><field>512</field><field>8</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*119"><field>MicrosoftExtensionsConfigurationCommandLinedll</field><field>MicrosoftExtensionsConfigurationCommandLinedll</field><field>d2z1bt1v.dll|Microsoft.Extensions.Configuration.CommandLine.dll</field><field>24736</field><field>8.0.23.53103</field><field>0</field><field>512</field><field>9</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*123"><field>MicrosoftExtensionsConfigurationdll</field><field>MicrosoftExtensionsConfigurationdll</field><field>mmnlbsim.dll|Microsoft.Extensions.Configuration.dll</field><field>43800</field><field>8.0.23.53103</field><field>0</field><field>512</field><field>10</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*127"><field>MicrosoftExtensionsConfigurationEnvironmentVariablesdll</field><field>MicrosoftExtensionsConfigurationEnvironmentVariablesdll</field><field>dalmsvus.dll|Microsoft.Extensions.Configuration.EnvironmentVariables.dll</field><field>21280</field><field>8.0.23.53103</field><field>0</field><field>512</field><field>11</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*131"><field>MicrosoftExtensionsConfigurationFileExtensionsdll</field><field>MicrosoftExtensionsConfigurationFileExtensionsdll</field><field>vlv3hlzr.dll|Microsoft.Extensions.Configuration.FileExtensions.dll</field><field>27936</field><field>8.0.724.31311</field><field>0</field><field>512</field><field>12</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*135"><field>MicrosoftExtensionsConfigurationJsondll</field><field>MicrosoftExtensionsConfigurationJsondll</field><field>ivglcmkq.dll|Microsoft.Extensions.Configuration.Json.dll</field><field>26920</field><field>8.0.1024.46610</field><field>0</field><field>512</field><field>13</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*139"><field>MicrosoftExtensionsConfigurationUserSecretsdll</field><field>MicrosoftExtensionsConfigurationUserSecretsdll</field><field>8ngakjjs.dll|Microsoft.Extensions.Configuration.UserSecrets.dll</field><field>25384</field><field>8.0.1024.46610</field><field>0</field><field>512</field><field>14</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*143"><field>MicrosoftExtensionsDependencyInjectionAbstractionsdll</field><field>MicrosoftExtensionsDependencyInjectionAbstractionsdll</field><field>ehvekaam.dll|Microsoft.Extensions.DependencyInjection.Abstractions.dll</field><field>63768</field><field>8.0.1024.46610</field><field>0</field><field>512</field><field>15</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*147"><field>MicrosoftExtensionsDependencyInjectiondll</field><field>MicrosoftExtensionsDependencyInjectiondll</field><field>npffeenl.dll|Microsoft.Extensions.DependencyInjection.dll</field><field>92952</field><field>8.0.1024.46610</field><field>0</field><field>512</field><field>16</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*151"><field>MicrosoftExtensionsDiagnosticsAbstractionsdll</field><field>MicrosoftExtensionsDiagnosticsAbstractionsdll</field><field>xu-r2zwm.dll|Microsoft.Extensions.Diagnostics.Abstractions.dll</field><field>30480</field><field>8.0.1024.46610</field><field>0</field><field>512</field><field>17</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*155"><field>MicrosoftExtensionsDiagnosticsdll</field><field>MicrosoftExtensionsDiagnosticsdll</field><field>jaehtjpp.dll|Microsoft.Extensions.Diagnostics.dll</field><field>35592</field><field>8.0.1024.46610</field><field>0</field><field>512</field><field>18</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*159"><field>MicrosoftExtensionsFileProvidersAbstractionsdll</field><field>MicrosoftExtensionsFileProvidersAbstractionsdll</field><field>f4jgxinf.dll|Microsoft.Extensions.FileProviders.Abstractions.dll</field><field>22176</field><field>8.0.23.53103</field><field>0</field><field>512</field><field>19</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*163"><field>MicrosoftExtensionsFileProvidersPhysicaldll</field><field>MicrosoftExtensionsFileProvidersPhysicaldll</field><field>dzgf034p.dll|Microsoft.Extensions.FileProviders.Physical.dll</field><field>44808</field><field>8.0.23.53103</field><field>0</field><field>512</field><field>20</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*167"><field>MicrosoftExtensionsFileSystemGlobbingdll</field><field>MicrosoftExtensionsFileSystemGlobbingdll</field><field>id2aqrsp.dll|Microsoft.Extensions.FileSystemGlobbing.dll</field><field>45848</field><field>8.0.23.53103</field><field>0</field><field>512</field><field>21</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*171"><field>MicrosoftExtensionsHostingAbstractionsdll</field><field>MicrosoftExtensionsHostingAbstractionsdll</field><field>r8_i9ah0.dll|Microsoft.Extensions.Hosting.Abstractions.dll</field><field>51472</field><field>8.0.1024.46610</field><field>0</field><field>512</field><field>22</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*175"><field>MicrosoftExtensionsHostingdll</field><field>MicrosoftExtensionsHostingdll</field><field>bqwvjunh.dll|Microsoft.Extensions.Hosting.dll</field><field>72488</field><field>8.0.1024.46610</field><field>0</field><field>512</field><field>23</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*179"><field>MicrosoftExtensionsHostingWindowsServicesdll</field><field>MicrosoftExtensionsHostingWindowsServicesdll</field><field>hkio-3xt.dll|Microsoft.Extensions.Hosting.WindowsServices.dll</field><field>29872</field><field>8.0.1024.46610</field><field>0</field><field>512</field><field>24</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*183"><field>MicrosoftExtensionsHttpdll</field><field>MicrosoftExtensionsHttpdll</field><field>kl8vjmv7.dll|Microsoft.Extensions.Http.dll</field><field>88856</field><field>8.0.1024.46610</field><field>0</field><field>512</field><field>25</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*187"><field>MicrosoftExtensionsLoggingAbstractionsdll</field><field>MicrosoftExtensionsLoggingAbstractionsdll</field><field>np1r3g_m.dll|Microsoft.Extensions.Logging.Abstractions.dll</field><field>65320</field><field>8.0.1024.46610</field><field>0</field><field>512</field><field>26</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*191"><field>MicrosoftExtensionsLoggingConfigurationdll</field><field>MicrosoftExtensionsLoggingConfigurationdll</field><field>ig21c2gu.dll|Microsoft.Extensions.Logging.Configuration.dll</field><field>27912</field><field>8.0.1024.46610</field><field>0</field><field>512</field><field>27</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*195"><field>MicrosoftExtensionsLoggingConsoledll</field><field>MicrosoftExtensionsLoggingConsoledll</field><field>0nqdkbxo.dll|Microsoft.Extensions.Logging.Console.dll</field><field>71464</field><field>8.0.1024.46610</field><field>0</field><field>512</field><field>28</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*199"><field>MicrosoftExtensionsLoggingDebugdll</field><field>MicrosoftExtensionsLoggingDebugdll</field><field>ddb6ldpv.dll|Microsoft.Extensions.Logging.Debug.dll</field><field>20248</field><field>8.0.1024.46610</field><field>0</field><field>512</field><field>29</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*203"><field>MicrosoftExtensionsLoggingdll</field><field>MicrosoftExtensionsLoggingdll</field><field>nyc6rbah.dll|Microsoft.Extensions.Logging.dll</field><field>50976</field><field>8.0.1024.46610</field><field>0</field><field>512</field><field>30</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*207"><field>MicrosoftExtensionsLoggingEventLogdll</field><field>MicrosoftExtensionsLoggingEventLogdll</field><field>hn6kg_45.dll|Microsoft.Extensions.Logging.EventLog.dll</field><field>25760</field><field>8.0.1024.46610</field><field>0</field><field>512</field><field>31</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*211"><field>MicrosoftExtensionsLoggingEventSourcedll</field><field>MicrosoftExtensionsLoggingEventSourcedll</field><field>pz-xu9u6.dll|Microsoft.Extensions.Logging.EventSource.dll</field><field>34568</field><field>8.0.1024.46610</field><field>0</field><field>512</field><field>32</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*215"><field>MicrosoftExtensionsOptionsConfigurationExtensionsdll</field><field>MicrosoftExtensionsOptionsConfigurationExtensionsdll</field><field>yyk0gtti.dll|Microsoft.Extensions.Options.ConfigurationExtensions.dll</field><field>22688</field><field>8.0.23.53103</field><field>0</field><field>512</field><field>33</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*219"><field>MicrosoftExtensionsOptionsdll</field><field>MicrosoftExtensionsOptionsdll</field><field>ar2_4vm4.dll|Microsoft.Extensions.Options.dll</field><field>64776</field><field>8.0.224.6711</field><field>0</field><field>512</field><field>34</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*223"><field>MicrosoftExtensionsPrimitivesdll</field><field>MicrosoftExtensionsPrimitivesdll</field><field>bgvzwz9p.dll|Microsoft.Extensions.Primitives.dll</field><field>43680</field><field>8.0.23.53103</field><field>0</field><field>512</field><field>35</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*227"><field>SystemCodeDomdll</field><field>SystemCodeDomdll</field><field>gqy_fxmy.dll|System.CodeDom.dll</field><field>184080</field><field>8.0.23.53103</field><field>0</field><field>512</field><field>37</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*231"><field>SystemConfigurationConfigurationManagerdll</field><field>SystemConfigurationConfigurationManagerdll</field><field>vsyru0sj.dll|System.Configuration.ConfigurationManager.dll</field><field>442632</field><field>8.0.23.53103</field><field>0</field><field>512</field><field>38</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*235"><field>SystemDiagnosticsEventLogdll</field><field>SystemDiagnosticsEventLogdll</field><field>o8mzm6dl.dll|System.Diagnostics.EventLog.dll</field><field>51992</field><field>8.0.1024.46610</field><field>0</field><field>512</field><field>39</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*239"><field>SystemDiagnosticsPerformanceCounterdll</field><field>SystemDiagnosticsPerformanceCounterdll</field><field>wxwg1er7.dll|System.Diagnostics.PerformanceCounter.dll</field><field>51872</field><field>8.0.23.53103</field><field>0</field><field>512</field><field>40</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*243"><field>SystemIOPipelinesdll</field><field>SystemIOPipelinesdll</field><field>exevpuqa.dll|System.IO.Pipelines.dll</field><field>77584</field><field>9.0.525.21509</field><field>0</field><field>512</field><field>41</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*247"><field>SystemManagementdll</field><field>SystemManagementdll</field><field>fayafg1p.dll|System.Management.dll</field><field>72968</field><field>8.0.23.53103</field><field>0</field><field>512</field><field>42</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*251"><field>SystemSecurityCryptographyProtectedDatadll</field><field>SystemSecurityCryptographyProtectedDatadll</field><field>c8bh91cg.dll|System.Security.Cryptography.ProtectedData.dll</field><field>36616</field><field>8.0.23.53103</field><field>0</field><field>512</field><field>43</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*255"><field>SystemServiceProcessServiceControllerdll</field><field>SystemServiceProcessServiceControllerdll</field><field>vvfhfvv4.dll|System.ServiceProcess.ServiceController.dll</field><field>33440</field><field>8.0.1024.46610</field><field>0</field><field>512</field><field>44</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*259"><field>SystemTextEncodingsWebdll</field><field>SystemTextEncodingsWebdll</field><field>zflh7ahv.dll|System.Text.Encodings.Web.dll</field><field>70952</field><field>9.0.525.21509</field><field>0</field><field>512</field><field>45</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*263"><field>SystemTextJsondll</field><field>SystemTextJsondll</field><field>d9hmqlsk.dll|System.Text.Json.dll</field><field>643848</field><field>9.0.525.21509</field><field>0</field><field>512</field><field>46</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*267"><field>YamlDotNetdll</field><field>YamlDotNetdll</field><field>af2l4na4.dll|YamlDotNet.dll</field><field>293376</field><field>16.3.0.0</field><field>0</field><field>512</field><field>47</field></row></table><table name="InstallExecuteSequence" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row><field>FindRelatedProducts</field><field /><field>25</field></row><row><field>LaunchConditions</field><field /><field>100</field></row><row><field>ValidateProductID</field><field /><field>700</field></row><row><field>CostInitialize</field><field /><field>800</field></row><row><field>FileCost</field><field /><field>900</field></row><row><field>CostFinalize</field><field /><field>1000</field></row><row><field>MigrateFeatureStates</field><field /><field>1200</field></row><row><field>InstallValidate</field><field /><field>1400</field></row><row><field>InstallInitialize</field><field /><field>1500</field></row><row><field>ProcessComponents</field><field /><field>1600</field></row><row><field>UnpublishFeatures</field><field /><field>1800</field></row><row><field>StopServices</field><field>VersionNT</field><field>1900</field></row><row><field>DeleteServices</field><field>VersionNT</field><field>2000</field></row><row><field>RemoveFiles</field><field /><field>3500</field></row><row><field>InstallFiles</field><field /><field>4000</field></row><row><field>InstallServices</field><field>VersionNT</field><field>5800</field></row><row><field>StartServices</field><field>VersionNT</field><field>5900</field></row><row><field>RegisterUser</field><field /><field>6000</field></row><row><field>RegisterProduct</field><field /><field>6100</field></row><row><field>PublishFeatures</field><field /><field>6300</field></row><row><field>PublishProduct</field><field /><field>6400</field></row><row><field>InstallFinalize</field><field /><field>6600</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*19"><field>RemoveExistingProducts</field><field /><field>1501</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*54"><field>SetServiceTimeout</field><field>NOT Installed</field><field>5801</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*55"><field>SetServiceConfig</field><field>NOT Installed</field><field>5802</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*56"><field>SetServiceFailureActions</field><field>NOT Installed</field><field>5803</field></row></table><table name="InstallUISequence" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row><field>FindRelatedProducts</field><field /><field>25</field></row><row><field>LaunchConditions</field><field /><field>100</field></row><row><field>ValidateProductID</field><field /><field>700</field></row><row><field>CostInitialize</field><field /><field>800</field></row><row><field>FileCost</field><field /><field>900</field></row><row><field>CostFinalize</field><field /><field>1000</field></row><row><field>MigrateFeatureStates</field><field /><field>1200</field></row><row><field>ExecuteAction</field><field /><field>1300</field></row></table><table name="LaunchCondition" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*19"><field>NOT WIX_DOWNGRADE_DETECTED</field><field>A newer version is already installed.</field></row></table><table name="Media" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*16"><field>1</field><field>47</field><field /><field>#files.cab</field><field /><field /></row></table><table name="MsiFileHash" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*87"><field>LSBLogMonitorServicedepsjson</field><field>0</field><field>1903400676</field><field>-1550847139</field><field>1550238713</field><field>-862587923</field></row><row sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*91"><field>LSBLogMonitorServiceruntimeconfigjson</field><field>0</field><field>-1724697819</field><field>-721976706</field><field>755476558</field><field>970712826</field></row><row sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*95"><field>LSBLogMonitorServicepdb</field><field>0</field><field>-1377000023</field><field>95245168</field><field>-1922983515</field><field>-1926181618</field></row><row sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*99"><field>appsettingsjson</field><field>0</field><field>2105582580</field><field>1119749595</field><field>-1120280015</field><field>1315624272</field></row><row sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*103"><field>appsettingsDevelopmentjson</field><field>0</field><field>-935151121</field><field>-1693938056</field><field>-1077194194</field><field>2118889712</field></row></table><table name="Property" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*12"><field>ALLUSERS</field><field>1</field></row><row sectionId="*.Manufacturer" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*5"><field>Manufacturer</field><field>LSB</field></row><row sectionId="*.ProductCode" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*5"><field>ProductCode</field><field>{AABE37E6-3A5B-4122-827D-4363E99274C0}</field></row><row sectionId="*.ProductLanguage" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*5"><field>ProductLanguage</field><field>1033</field></row><row sectionId="*.ProductName" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*5"><field>ProductName</field><field>LSB Telemetry Service</field></row><row sectionId="*.ProductVersion" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*5"><field>ProductVersion</field><field>1.0.0.0</field></row><row sectionId="*.UpgradeCode" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*5"><field>UpgradeCode</field><field>{EB8A2174-7977-45CB-8EDB-936FBC25378E}</field></row><row><field>SecureCustomProperties</field><field>WIX_DOWNGRADE_DETECTED;WIX_UPGRADE_DETECTED</field></row></table><table name="ServiceControl" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*79"><field>ServiceControl</field><field>LSBTelemetryService</field><field>162</field><field /><field>1</field><field>ServiceExe</field></row></table><table name="ServiceInstall" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*70"><field>InstallService</field><field>LSBTelemetryService</field><field>LSB Telemetry Service</field><field>16</field><field>2</field><field>1</field><field /><field /><field>LocalSystem</field><field /><field /><field>ServiceExe</field><field>LSB Telemetry Service - System monitoring</field></row></table><table name="Upgrade" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*19"><field>{EB8A2174-7977-45CB-8EDB-936FBC25378E}</field><field /><field>1.0.0.0</field><field /><field>1</field><field /><field>WIX_UPGRADE_DETECTED</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*19"><field>{EB8A2174-7977-45CB-8EDB-936FBC25378E}</field><field>1.0.0.0</field><field /><field /><field>2</field><field /><field>WIX_DOWNGRADE_DETECTED</field></row></table><table name="WixAction" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*19"><field>InstallExecuteSequence</field><field>RemoveExistingProducts</field><field /><field>1501</field><field /><field>InstallInitialize</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*54"><field>InstallExecuteSequence</field><field>SetServiceTimeout</field><field>NOT Installed</field><field>5801</field><field /><field>InstallServices</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*55"><field>InstallExecuteSequence</field><field>SetServiceConfig</field><field>NOT Installed</field><field>5802</field><field /><field>SetServiceTimeout</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*56"><field>InstallExecuteSequence</field><field>SetServiceFailureActions</field><field>NOT Installed</field><field>5803</field><field /><field>SetServiceConfig</field><field>0</field></row></table><table name="WixBuildInfo" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row><field>3.14.1.8722</field><field>D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\bin\Release\LSB.LogMonitor.Service.Setup.msi</field><field>D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\LSB.LogMonitor.Service.Setup.wixproj</field><field>D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\bin\Release\LSB.LogMonitor.Service.Setup.wixpdb</field></row></table><table name="WixComplexReference" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*30"><field>*</field><field>5</field><field /><field>Complete</field><field>2</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*86"><field>Complete</field><field>1</field><field /><field>LSBLogMonitorServicedepsjson</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*106"><field>Complete</field><field>1</field><field /><field>LSBLogMonitorServicedll</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*94"><field>Complete</field><field>1</field><field /><field>LSBLogMonitorServicepdb</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*90"><field>Complete</field><field>1</field><field /><field>LSBLogMonitorServiceruntimeconfigjson</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*110"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsConfigurationAbstractionsdll</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*114"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsConfigurationBinderdll</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*118"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsConfigurationCommandLinedll</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*126"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsConfigurationEnvironmentVariablesdll</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*130"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsConfigurationFileExtensionsdll</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*134"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsConfigurationJsondll</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*138"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsConfigurationUserSecretsdll</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*122"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsConfigurationdll</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*142"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsDependencyInjectionAbstractionsdll</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*146"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsDependencyInjectiondll</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*150"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsDiagnosticsAbstractionsdll</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*154"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsDiagnosticsdll</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*158"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsFileProvidersAbstractionsdll</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*162"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsFileProvidersPhysicaldll</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*166"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsFileSystemGlobbingdll</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*170"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsHostingAbstractionsdll</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*178"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsHostingWindowsServicesdll</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*174"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsHostingdll</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*182"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsHttpdll</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*186"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsLoggingAbstractionsdll</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*190"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsLoggingConfigurationdll</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*194"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsLoggingConsoledll</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*198"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsLoggingDebugdll</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*206"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsLoggingEventLogdll</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*210"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsLoggingEventSourcedll</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*202"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsLoggingdll</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*214"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsOptionsConfigurationExtensionsdll</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*218"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsOptionsdll</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*222"><field>Complete</field><field>1</field><field /><field>MicrosoftExtensionsPrimitivesdll</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*64"><field>Complete</field><field>1</field><field /><field>ServiceExe</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*226"><field>Complete</field><field>1</field><field /><field>SystemCodeDomdll</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*230"><field>Complete</field><field>1</field><field /><field>SystemConfigurationConfigurationManagerdll</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*234"><field>Complete</field><field>1</field><field /><field>SystemDiagnosticsEventLogdll</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*238"><field>Complete</field><field>1</field><field /><field>SystemDiagnosticsPerformanceCounterdll</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*242"><field>Complete</field><field>1</field><field /><field>SystemIOPipelinesdll</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*246"><field>Complete</field><field>1</field><field /><field>SystemManagementdll</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*250"><field>Complete</field><field>1</field><field /><field>SystemSecurityCryptographyProtectedDatadll</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*254"><field>Complete</field><field>1</field><field /><field>SystemServiceProcessServiceControllerdll</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*258"><field>Complete</field><field>1</field><field /><field>SystemTextEncodingsWebdll</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*262"><field>Complete</field><field>1</field><field /><field>SystemTextJsondll</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*266"><field>Complete</field><field>1</field><field /><field>YamlDotNetdll</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*102"><field>Complete</field><field>1</field><field /><field>appsettingsDevelopmentjson</field><field>1</field><field>0</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*98"><field>Complete</field><field>1</field><field /><field>appsettingsjson</field><field>1</field><field>0</field></row></table><table name="WixComponentGroup" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*62"><field>ServiceFiles</field></row></table><table name="WixFile" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*65"><field>ServiceExe</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\LSB.LogMonitor.Service.exe">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\LSB.LogMonitor.Service.exe</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*87"><field>LSBLogMonitorServicedepsjson</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\LSB.LogMonitor.Service.deps.json">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\LSB.LogMonitor.Service.deps.json</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*91"><field>LSBLogMonitorServiceruntimeconfigjson</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\LSB.LogMonitor.Service.runtimeconfig.json">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\LSB.LogMonitor.Service.runtimeconfig.json</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*95"><field>LSBLogMonitorServicepdb</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\LSB.LogMonitor.Service.pdb">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\LSB.LogMonitor.Service.pdb</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*99"><field>appsettingsjson</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\appsettings.json">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\appsettings.json</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*103"><field>appsettingsDevelopmentjson</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\appsettings.Development.json">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\appsettings.Development.json</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*107"><field>LSBLogMonitorServicedll</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\LSB.LogMonitor.Service.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\LSB.LogMonitor.Service.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*111"><field>MicrosoftExtensionsConfigurationAbstractionsdll</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.Configuration.Abstractions.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.Configuration.Abstractions.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*115"><field>MicrosoftExtensionsConfigurationBinderdll</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.Configuration.Binder.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.Configuration.Binder.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*119"><field>MicrosoftExtensionsConfigurationCommandLinedll</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.Configuration.CommandLine.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.Configuration.CommandLine.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*123"><field>MicrosoftExtensionsConfigurationdll</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.Configuration.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.Configuration.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*127"><field>MicrosoftExtensionsConfigurationEnvironmentVariablesdll</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.Configuration.EnvironmentVariables.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.Configuration.EnvironmentVariables.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*131"><field>MicrosoftExtensionsConfigurationFileExtensionsdll</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.Configuration.FileExtensions.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.Configuration.FileExtensions.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*135"><field>MicrosoftExtensionsConfigurationJsondll</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.Configuration.Json.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.Configuration.Json.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*139"><field>MicrosoftExtensionsConfigurationUserSecretsdll</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.Configuration.UserSecrets.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.Configuration.UserSecrets.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*143"><field>MicrosoftExtensionsDependencyInjectionAbstractionsdll</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*147"><field>MicrosoftExtensionsDependencyInjectiondll</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.DependencyInjection.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.DependencyInjection.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*151"><field>MicrosoftExtensionsDiagnosticsAbstractionsdll</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.Diagnostics.Abstractions.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.Diagnostics.Abstractions.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*155"><field>MicrosoftExtensionsDiagnosticsdll</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.Diagnostics.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.Diagnostics.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*159"><field>MicrosoftExtensionsFileProvidersAbstractionsdll</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.FileProviders.Abstractions.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.FileProviders.Abstractions.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*163"><field>MicrosoftExtensionsFileProvidersPhysicaldll</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.FileProviders.Physical.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.FileProviders.Physical.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*167"><field>MicrosoftExtensionsFileSystemGlobbingdll</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.FileSystemGlobbing.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.FileSystemGlobbing.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*171"><field>MicrosoftExtensionsHostingAbstractionsdll</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.Hosting.Abstractions.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.Hosting.Abstractions.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*175"><field>MicrosoftExtensionsHostingdll</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.Hosting.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.Hosting.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*179"><field>MicrosoftExtensionsHostingWindowsServicesdll</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.Hosting.WindowsServices.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.Hosting.WindowsServices.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*183"><field>MicrosoftExtensionsHttpdll</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.Http.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.Http.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*187"><field>MicrosoftExtensionsLoggingAbstractionsdll</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.Logging.Abstractions.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.Logging.Abstractions.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*191"><field>MicrosoftExtensionsLoggingConfigurationdll</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.Logging.Configuration.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.Logging.Configuration.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*195"><field>MicrosoftExtensionsLoggingConsoledll</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.Logging.Console.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.Logging.Console.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*199"><field>MicrosoftExtensionsLoggingDebugdll</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.Logging.Debug.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.Logging.Debug.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*203"><field>MicrosoftExtensionsLoggingdll</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.Logging.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.Logging.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*207"><field>MicrosoftExtensionsLoggingEventLogdll</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.Logging.EventLog.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.Logging.EventLog.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*211"><field>MicrosoftExtensionsLoggingEventSourcedll</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.Logging.EventSource.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.Logging.EventSource.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*215"><field>MicrosoftExtensionsOptionsConfigurationExtensionsdll</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.Options.ConfigurationExtensions.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.Options.ConfigurationExtensions.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*219"><field>MicrosoftExtensionsOptionsdll</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.Options.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.Options.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*223"><field>MicrosoftExtensionsPrimitivesdll</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.Primitives.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.Primitives.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*227"><field>SystemCodeDomdll</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\System.CodeDom.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\System.CodeDom.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*231"><field>SystemConfigurationConfigurationManagerdll</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\System.Configuration.ConfigurationManager.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\System.Configuration.ConfigurationManager.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*235"><field>SystemDiagnosticsEventLogdll</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\System.Diagnostics.EventLog.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\System.Diagnostics.EventLog.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*239"><field>SystemDiagnosticsPerformanceCounterdll</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\System.Diagnostics.PerformanceCounter.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\System.Diagnostics.PerformanceCounter.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*243"><field>SystemIOPipelinesdll</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\System.IO.Pipelines.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\System.IO.Pipelines.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*247"><field>SystemManagementdll</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\System.Management.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\System.Management.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*251"><field>SystemSecurityCryptographyProtectedDatadll</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\System.Security.Cryptography.ProtectedData.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\System.Security.Cryptography.ProtectedData.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*255"><field>SystemServiceProcessServiceControllerdll</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\System.ServiceProcess.ServiceController.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\System.ServiceProcess.ServiceController.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*259"><field>SystemTextEncodingsWebdll</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\System.Text.Encodings.Web.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\System.Text.Encodings.Web.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*263"><field>SystemTextJsondll</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\System.Text.Json.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\System.Text.Json.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*267"><field>YamlDotNetdll</field><field /><field /><field /><field>INSTALLFOLDER</field><field>1</field><field unresolvedData="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\YamlDotNet.dll">D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\YamlDotNet.dll</field><field /><field>-1</field><field>1</field><field>0</field><field /><field /><field /><field /></row></table><table name="WixGroup" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*31"><field>Complete</field><field>Feature</field><field>ServiceFiles</field><field>ComponentGroup</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*30"><field>*</field><field>Product</field><field>Complete</field><field>Feature</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*64"><field>ServiceFiles</field><field>ComponentGroup</field><field>ServiceExe</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*86"><field>ServiceFiles</field><field>ComponentGroup</field><field>LSBLogMonitorServicedepsjson</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*90"><field>ServiceFiles</field><field>ComponentGroup</field><field>LSBLogMonitorServiceruntimeconfigjson</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*94"><field>ServiceFiles</field><field>ComponentGroup</field><field>LSBLogMonitorServicepdb</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*98"><field>ServiceFiles</field><field>ComponentGroup</field><field>appsettingsjson</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*102"><field>ServiceFiles</field><field>ComponentGroup</field><field>appsettingsDevelopmentjson</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*106"><field>ServiceFiles</field><field>ComponentGroup</field><field>LSBLogMonitorServicedll</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*110"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsConfigurationAbstractionsdll</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*114"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsConfigurationBinderdll</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*118"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsConfigurationCommandLinedll</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*122"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsConfigurationdll</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*126"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsConfigurationEnvironmentVariablesdll</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*130"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsConfigurationFileExtensionsdll</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*134"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsConfigurationJsondll</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*138"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsConfigurationUserSecretsdll</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*142"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsDependencyInjectionAbstractionsdll</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*146"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsDependencyInjectiondll</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*150"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsDiagnosticsAbstractionsdll</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*154"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsDiagnosticsdll</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*158"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsFileProvidersAbstractionsdll</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*162"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsFileProvidersPhysicaldll</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*166"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsFileSystemGlobbingdll</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*170"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsHostingAbstractionsdll</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*174"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsHostingdll</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*178"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsHostingWindowsServicesdll</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*182"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsHttpdll</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*186"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsLoggingAbstractionsdll</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*190"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsLoggingConfigurationdll</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*194"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsLoggingConsoledll</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*198"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsLoggingDebugdll</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*202"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsLoggingdll</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*206"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsLoggingEventLogdll</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*210"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsLoggingEventSourcedll</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*214"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsOptionsConfigurationExtensionsdll</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*218"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsOptionsdll</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*222"><field>ServiceFiles</field><field>ComponentGroup</field><field>MicrosoftExtensionsPrimitivesdll</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*226"><field>ServiceFiles</field><field>ComponentGroup</field><field>SystemCodeDomdll</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*230"><field>ServiceFiles</field><field>ComponentGroup</field><field>SystemConfigurationConfigurationManagerdll</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*234"><field>ServiceFiles</field><field>ComponentGroup</field><field>SystemDiagnosticsEventLogdll</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*238"><field>ServiceFiles</field><field>ComponentGroup</field><field>SystemDiagnosticsPerformanceCounterdll</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*242"><field>ServiceFiles</field><field>ComponentGroup</field><field>SystemIOPipelinesdll</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*246"><field>ServiceFiles</field><field>ComponentGroup</field><field>SystemManagementdll</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*250"><field>ServiceFiles</field><field>ComponentGroup</field><field>SystemSecurityCryptographyProtectedDatadll</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*254"><field>ServiceFiles</field><field>ComponentGroup</field><field>SystemServiceProcessServiceControllerdll</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*258"><field>ServiceFiles</field><field>ComponentGroup</field><field>SystemTextEncodingsWebdll</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*262"><field>ServiceFiles</field><field>ComponentGroup</field><field>SystemTextJsondll</field><field>Component</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*266"><field>ServiceFiles</field><field>ComponentGroup</field><field>YamlDotNetdll</field><field>Component</field></row></table><table name="WixSimpleReference" xmlns="http://schemas.microsoft.com/wix/2006/objects"><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*5"><field>Property</field><field>Manufacturer</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*5"><field>Property</field><field>ProductCode</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*5"><field>Property</field><field>ProductLanguage</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*5"><field>Property</field><field>ProductName</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*5"><field>Property</field><field>ProductVersion</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*5"><field>Property</field><field>UpgradeCode</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*31"><field>WixComponentGroup</field><field>ServiceFiles</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*35"><field>Directory</field><field>INSTALLFOLDER</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*41"><field>Directory</field><field>INSTALLFOLDER</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*47"><field>Directory</field><field>INSTALLFOLDER</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*54"><field>CustomAction</field><field>SetServiceTimeout</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*54"><field>WixAction</field><field>InstallExecuteSequence/InstallServices</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*55"><field>CustomAction</field><field>SetServiceConfig</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*55"><field>WixAction</field><field>InstallExecuteSequence/SetServiceTimeout</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*56"><field>CustomAction</field><field>SetServiceFailureActions</field></row><row sectionId="*" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*56"><field>WixAction</field><field>InstallExecuteSequence/SetServiceConfig</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*62"><field>Directory</field><field>INSTALLFOLDER</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*65"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*87"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*91"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*95"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*99"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*103"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*107"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*111"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*115"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*119"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*123"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*127"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*131"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*135"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*139"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*143"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*147"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*151"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*155"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*159"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*163"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*167"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*171"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*175"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*179"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*183"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*187"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*191"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*195"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*199"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*203"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*207"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*211"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*215"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*219"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*223"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*227"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*231"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*235"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*239"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*243"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*247"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*251"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*255"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*259"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*263"><field>Media</field><field>1</field></row><row sectionId="wix.section.8" sourceLineNumber="D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service.Setup\Product.wxs*267"><field>Media</field><field>1</field></row></table></wixOutput></wixPdb>