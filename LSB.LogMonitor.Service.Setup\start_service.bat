﻿@echo off
echo Starting LSB Telemetry Service...

REM Set longer timeout for service startup
reg add "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control" /v "ServicesPipeTimeout" /t REG_DWORD /d "120000" /f

REM Start service with extended timeout
echo Please wait, service is starting...
sc start LSBTelemetryService

REM Wait and check status
timeout /t 10
sc query LSBTelemetryService

echo.
echo If service shows "RUNNING", it started successfully!
echo If it shows "START_PENDING", please wait a bit longer.
pause