@echo off
echo ========================================
echo Setup Visual Studio Environment
echo ========================================
echo.

echo Checking Visual Studio build environment...
echo.

echo [1/5] Checking WiX Toolset...
where heat.exe >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ WiX Toolset not found in PATH
    echo.
    echo Please install WiX Toolset:
    echo 1. Download from: https://wixtoolset.org/releases/
    echo 2. Install WiX Toolset v3.11 or newer
    echo 3. Restart Visual Studio
    echo.
    set MISSING_TOOLS=1
) else (
    echo ✅ WiX Toolset found
)

echo.
echo [2/5] Checking .NET SDK...
dotnet --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ .NET SDK not found
    echo Please install .NET 8.0 SDK
    set MISSING_TOOLS=1
) else (
    for /f "tokens=*" %%i in ('dotnet --version') do set DOTNET_VERSION=%%i
    echo ✅ .NET SDK found: %DOTNET_VERSION%
)

echo.
echo [3/5] Checking MSBuild...
where msbuild.exe >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ MSBuild not found in PATH
    echo Please install Visual Studio Build Tools or Visual Studio
    set MISSING_TOOLS=1
) else (
    echo ✅ MSBuild found
)

echo.
echo [4/5] Checking project files...
if not exist "..\LSB.LogMonitor.Service\LSB.LogMonitor.Service.csproj" (
    echo ❌ Service project not found
    set MISSING_TOOLS=1
) else (
    echo ✅ Service project found
)

if not exist "LSB.LogMonitor.Service.Setup.wixproj" (
    echo ❌ Installer project not found
    set MISSING_TOOLS=1
) else (
    echo ✅ Installer project found
)

if not exist "Product.wxs" (
    echo ❌ Product.wxs not found
    set MISSING_TOOLS=1
) else (
    echo ✅ Product.wxs found
)

if not exist "ServiceFiles.xslt" (
    echo ❌ ServiceFiles.xslt not found
    set MISSING_TOOLS=1
) else (
    echo ✅ ServiceFiles.xslt found
)

echo.
echo [5/5] Testing Heat manually...
if defined MISSING_TOOLS (
    echo ⚠️  Skipping Heat test due to missing tools
) else (
    echo Testing Heat command...
    heat.exe >nul 2>&1
    if %ERRORLEVEL% NEQ 0 (
        echo ❌ Heat command failed
        set MISSING_TOOLS=1
    ) else (
        echo ✅ Heat command works
    )
)

echo.
echo ========================================
if defined MISSING_TOOLS (
    echo ❌ SETUP INCOMPLETE
    echo Please resolve the issues above before building in Visual Studio.
    echo.
    echo Quick setup checklist:
    echo 1. Install WiX Toolset v3.11+
    echo 2. Install Visual Studio 2019/2022 with .NET workload
    echo 3. Install WiX Toolset Visual Studio Extension
    echo 4. Restart Visual Studio
) else (
    echo ✅ ENVIRONMENT READY
    echo.
    echo You can now build in Visual Studio:
    echo 1. Open LSB.LogMonitor.Service.sln in Visual Studio
    echo 2. Set configuration to Release
    echo 3. Right-click solution → Rebuild Solution
    echo 4. Or right-click LSB.LogMonitor.Service.Setup → Rebuild
    echo.
    echo Heat will automatically:
    echo - Publish service project with all dependencies
    echo - Harvest all files from publish directory
    echo - Generate ServiceFiles.wxs with all components
    echo - Build complete MSI installer
)
echo ========================================
echo.
pause
