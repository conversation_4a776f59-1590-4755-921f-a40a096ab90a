<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
                xmlns:wix="http://schemas.microsoft.com/wix/2006/wi">

  <!-- Identity transform - copy everything by default -->
  <xsl:template match="@*|node()">
    <xsl:copy>
      <xsl:apply-templates select="@*|node()"/>
    </xsl:copy>
  </xsl:template>

  <!-- Transform the component containing the service executable -->
  <xsl:template match="wix:Component[wix:File[@Source and contains(@Source, 'LSB.LogMonitor.Service.exe')]]">
    <xsl:copy>
      <xsl:apply-templates select="@*"/>
      
      <!-- Copy the File element -->
      <xsl:apply-templates select="wix:File"/>
      
      <!-- Add Service Installation -->
      <wix:ServiceInstall Id="InstallService"
                          Name="LSBTelemetryService"
                          DisplayName="LSB Telemetry Service"
                          Description="LSB Telemetry Service - System monitoring"
                          Type="ownProcess"
                          Start="auto"
                          ErrorControl="normal"
                          Account="LocalSystem" />

      <!-- Add Service Control -->
      <wix:ServiceControl Id="ServiceControl"
                          Name="LSBTelemetryService"
                          Stop="both"
                          Remove="uninstall"
                          Wait="yes" />
      
      <!-- Apply any other child elements -->
      <xsl:apply-templates select="node()[not(self::wix:File)]"/>
    </xsl:copy>
  </xsl:template>

  <!-- Remove Scripts directory and its contents -->
  <xsl:template match="wix:Component[wix:File[contains(@Source, 'Scripts\')]]" />
  <xsl:template match="wix:Directory[@Name='Scripts']" />

</xsl:stylesheet>
