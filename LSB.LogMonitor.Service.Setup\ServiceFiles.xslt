<?xml version="1.0" encoding="utf-8"?>
<xsl:stylesheet version="1.0"
                xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
                xmlns:wix="http://schemas.microsoft.com/wix/2006/wi"
                xmlns="http://schemas.microsoft.com/wix/2006/wi"
                exclude-result-prefixes="wix">

  <!-- Copy all nodes and attributes by default -->
  <xsl:template match="@*|node()">
    <xsl:copy>
      <xsl:apply-templates select="@*|node()"/>
    </xsl:copy>
  </xsl:template>

  <!-- Transform the component containing the main service executable -->
  <xsl:template match="wix:Component[wix:File[@Source and contains(@Source, 'LSB.LogMonitor.Service.exe')]]">
    <xsl:copy>
      <xsl:apply-templates select="@*"/>
      <xsl:apply-templates select="wix:File"/>
      
      <!-- Add Service Installation for Windows Service mode -->
      <!-- Note: Since we switched to console app, we don't need ServiceInstall -->
      <!-- But keeping this template in case you want to switch back to service -->
      <!--
      <ServiceInstall Id="InstallService"
                      Name="LSBLogMonitorService"
                      DisplayName="LSB Log Monitor Service"
                      Description="LSB Log Monitor Service - Automated log monitoring and notification system"
                      Type="ownProcess"
                      Start="auto"
                      ErrorControl="normal"
                      Account="LocalSystem" />

      <ServiceControl Id="ServiceControl"
                      Name="LSBLogMonitorService"
                      Stop="both"
                      Remove="uninstall"
                      Wait="yes" />
      -->
    </xsl:copy>
  </xsl:template>

  <!-- Preserve configuration files during upgrade -->
  <xsl:template match="wix:Component[wix:File[@Source and contains(@Source, 'appsettings.json')]]">
    <xsl:copy>
      <xsl:apply-templates select="@*"/>
      <wix:File>
        <xsl:apply-templates select="wix:File/@*"/>
        <!-- Don't overwrite existing config files -->
        <xsl:attribute name="NeverOverwrite">yes</xsl:attribute>
      </wix:File>
    </xsl:copy>
  </xsl:template>

  <!-- Preserve any custom config files -->
  <xsl:template match="wix:Component[wix:File[@Source and contains(@Source, '.config')]]">
    <xsl:copy>
      <xsl:apply-templates select="@*"/>
      <wix:File>
        <xsl:apply-templates select="wix:File/@*"/>
        <!-- Don't overwrite existing config files -->
        <xsl:attribute name="NeverOverwrite">yes</xsl:attribute>
      </wix:File>
    </xsl:copy>
  </xsl:template>

  <!-- Add registry entry for main executable -->
  <xsl:template match="wix:Component[wix:File[@Source and contains(@Source, 'LSB.LogMonitor.Service.exe')]]">
    <xsl:copy>
      <xsl:apply-templates select="@*"/>
      <xsl:apply-templates select="wix:File"/>
      
      <!-- Add registry entry to track installation -->
      <RegistryValue Root="HKLM" 
                     Key="SOFTWARE\LSB\LogMonitor" 
                     Name="ExecutablePath" 
                     Type="string" 
                     Value="[INSTALLFOLDER]LSB.LogMonitor.Service.exe" />
    </xsl:copy>
  </xsl:template>

</xsl:stylesheet>
