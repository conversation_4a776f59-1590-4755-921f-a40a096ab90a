# Visual Studio Build Instructions - LSB Telemetry Service Installer

## 🎯 Cách build bằng Visual Studio với đầy đủ DLL

### Bước 1: Mở Solution trong Visual Studio
1. Mở Visual Studio 2019/2022
2. Mở file `LSB.LogMonitor.Service.sln`
3. <PERSON><PERSON><PERSON> bảo có 2 projects:
   - `LSB.LogMonitor.Service` (C# Service Project)
   - `LSB.LogMonitor.Service.Setup` (WiX Installer Project)

### Bước 2: Cấu hình Build Configuration
1. Chọn **Release** configuration (không phải Debug)
2. Chọn **Any CPU** platform
3. Right-click solution → **Properties**
4. Trong **Configuration Properties > Configuration**:
   - <PERSON><PERSON><PERSON> bả<PERSON> cả 2 projects đều được check để build
   - Set build order: Service project trước, Setup project sau

### Bước 3: Build với Heat (Tự động)
**Cách 1: Build toàn bộ Solution**
1. Right-click solution → **Rebuild Solution**
2. Hoặc: **Build > Rebuild Solution**

**Cách 2: Build riêng Installer Project**
1. Right-click `LSB.LogMonitor.Service.Setup` project
2. Chọn **Rebuild**

### Bước 4: Theo dõi Build Process
Trong **Output Window**, bạn sẽ thấy:
```
[VS BUILD] Publishing service project to ensure all dependencies are available...
[VS BUILD] Running Heat to harvest files from publish directory...
[VS BUILD] Heat completed. Generated ServiceFiles.wxs with all dependencies.
```

### Bước 5: Kiểm tra kết quả
1. **File MSI được tạo:**
   ```
   LSB.LogMonitor.Service.Setup\bin\Release\LSB.LogMonitor.Service.Setup.msi
   ```

2. **File ServiceFiles.wxs được Heat tạo:**
   ```
   LSB.LogMonitor.Service.Setup\ServiceFiles.wxs
   ```

3. **Publish directory với tất cả DLLs:**
   ```
   LSB.LogMonitor.Service\bin\Release\net8.0\publish\
   ```

## 🔧 Cách hoạt động của Heat trong Visual Studio

### Quy trình tự động:
1. **PublishServiceProject Target** chạy trước
   - Tự động publish service project
   - Tạo thư mục `publish` với tất cả dependencies

2. **BeforeBuild Target** chạy Heat
   - Scan thư mục `publish`
   - Tạo `ServiceFiles.wxs` với tất cả components
   - Apply XSLT transform để thêm service installation

3. **WiX Compiler** build installer
   - Sử dụng `Product.wxs` + `ServiceFiles.wxs`
   - Tạo file MSI hoàn chỉnh

### Files quan trọng:
- `Product.wxs` - Header và cấu hình installer
- `ServiceFiles.xslt` - Transform để thêm service installation
- `ServiceFiles.wxs` - Generated by Heat (không edit thủ công)

## 🚨 Troubleshooting

### Lỗi "Heat not found"
**Giải pháp:**
1. Cài đặt WiX Toolset: https://wixtoolset.org/releases/
2. Restart Visual Studio
3. Kiểm tra PATH environment variable

### Lỗi "Publish directory not found"
**Giải pháp:**
1. Build service project trước: Right-click `LSB.LogMonitor.Service` → Build
2. Hoặc: Clean solution và rebuild all

### Lỗi "ServiceFiles.wxs not found"
**Giải pháp:**
1. Đảm bảo Heat target đã chạy thành công
2. Kiểm tra Output window cho error messages
3. Build lại installer project

### Lỗi "Missing DLLs after installation"
**Nguyên nhân:** Heat không harvest đúng
**Giải pháp:**
1. Đảm bảo publish directory có đầy đủ files
2. Kiểm tra `ServiceFiles.wxs` có chứa tất cả DLLs
3. Rebuild với Clean solution trước

## ✅ Verification Steps

### Sau khi build thành công:
1. **Kiểm tra MSI file:**
   ```
   File size > 10MB (chứa tất cả dependencies)
   ```

2. **Kiểm tra ServiceFiles.wxs:**
   ```
   Chứa 40+ components
   Có ServiceInstall cho LSB.LogMonitor.Service.exe
   ```

3. **Test installer:**
   ```
   Run MSI as Administrator
   Service được cài đặt và start tự động
   Không có lỗi missing DLL
   ```

## 🎯 Kết quả cuối cùng

✅ **MSI Installer hoàn chỉnh** với tất cả dependencies
✅ **Tự động harvest** bằng Heat - không cần maintain thủ công
✅ **Service registration** tự động
✅ **Build dễ dàng** trong Visual Studio
✅ **Không thiếu DLL** nào

## 📝 Notes
- Luôn build ở **Release** mode
- Heat sẽ tự động chạy mỗi khi build installer project
- ServiceFiles.wxs được tạo tự động - không edit thủ công
- Nếu thêm dependencies mới, Heat sẽ tự động bao gồm
