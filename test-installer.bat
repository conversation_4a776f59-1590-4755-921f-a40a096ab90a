@echo off
echo ========================================
echo Test LSB Log Monitor Installer
echo ========================================
echo.

echo This script will test the installer build process and verify all DLLs are included.
echo.

echo [1/5] Building installer...
cd LSB.LogMonitor.Service.Setup
call build-installer.bat
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Installer build failed
    cd ..
    pause
    exit /b 1
)
cd ..

echo.
echo [2/5] Checking installer file...
if exist "LSB.LogMonitor.Service.Setup\bin\Release\LSB.LogMonitor.Service.Setup.msi" (
    echo ✅ Installer file found
    for %%A in ("LSB.LogMonitor.Service.Setup\bin\Release\LSB.LogMonitor.Service.Setup.msi") do (
        echo    Size: %%~zA bytes
        if %%~zA LSS 5000000 (
            echo ⚠️  Warning: Installer seems small (^<5MB^). May be missing DLLs.
        ) else (
            echo ✅ Installer size looks good (^>5MB^). Likely includes all DLLs.
        )
    )
) else (
    echo ❌ Installer file not found
    pause
    exit /b 1
)

echo.
echo [3/5] Checking ServiceFiles.wxs content...
if exist "LSB.LogMonitor.Service.Setup\ServiceFiles.wxs" (
    echo ✅ ServiceFiles.wxs found
    
    for /f %%i in ('findstr /c:"<Component" "LSB.LogMonitor.Service.Setup\ServiceFiles.wxs"') do (
        echo ✅ Components: %%i
        if %%i LSS 20 (
            echo ⚠️  Warning: Low component count. May be missing DLLs.
        ) else (
            echo ✅ Good component count. Likely includes all DLLs.
        )
    )
    
    echo.
    echo Checking for key DLLs in ServiceFiles.wxs:
    findstr /i "Microsoft.Extensions.Hosting.dll" "LSB.LogMonitor.Service.Setup\ServiceFiles.wxs" >nul && echo ✅ Microsoft.Extensions.Hosting.dll || echo ❌ Microsoft.Extensions.Hosting.dll - MISSING
    findstr /i "Microsoft.Extensions.DependencyInjection.dll" "LSB.LogMonitor.Service.Setup\ServiceFiles.wxs" >nul && echo ✅ Microsoft.Extensions.DependencyInjection.dll || echo ❌ Microsoft.Extensions.DependencyInjection.dll - MISSING
    findstr /i "Microsoft.Extensions.Configuration.dll" "LSB.LogMonitor.Service.Setup\ServiceFiles.wxs" >nul && echo ✅ Microsoft.Extensions.Configuration.dll || echo ❌ Microsoft.Extensions.Configuration.dll - MISSING
    findstr /i "Microsoft.Extensions.Logging.dll" "LSB.LogMonitor.Service.Setup\ServiceFiles.wxs" >nul && echo ✅ Microsoft.Extensions.Logging.dll || echo ❌ Microsoft.Extensions.Logging.dll - MISSING
    findstr /i "System.Text.Json.dll" "LSB.LogMonitor.Service.Setup\ServiceFiles.wxs" >nul && echo ✅ System.Text.Json.dll || echo ❌ System.Text.Json.dll - MISSING
    findstr /i "YamlDotNet.dll" "LSB.LogMonitor.Service.Setup\ServiceFiles.wxs" >nul && echo ✅ YamlDotNet.dll || echo ❌ YamlDotNet.dll - MISSING
    findstr /i "LSB.LogMonitor.Service.exe" "LSB.LogMonitor.Service.Setup\ServiceFiles.wxs" >nul && echo ✅ LSB.LogMonitor.Service.exe || echo ❌ LSB.LogMonitor.Service.exe - MISSING
    findstr /i "appsettings.json" "LSB.LogMonitor.Service.Setup\ServiceFiles.wxs" >nul && echo ✅ appsettings.json || echo ❌ appsettings.json - MISSING
) else (
    echo ❌ ServiceFiles.wxs not found
)

echo.
echo [4/5] Checking publish directory...
set PUBLISH_DIR=LSB.LogMonitor.Service\bin\Release\net8.0\publish
if exist "%PUBLISH_DIR%" (
    echo ✅ Publish directory found: %PUBLISH_DIR%
    
    echo.
    echo Files in publish directory:
    for /f %%i in ('dir /b "%PUBLISH_DIR%\*.dll" 2^>nul ^| find /c /v ""') do echo ✅ DLL files: %%i
    for /f %%i in ('dir /b "%PUBLISH_DIR%\*.exe" 2^>nul ^| find /c /v ""') do echo ✅ EXE files: %%i
    for /f %%i in ('dir /b "%PUBLISH_DIR%\*.json" 2^>nul ^| find /c /v ""') do echo ✅ JSON files: %%i
    
    echo.
    echo Total files in publish directory:
    for /f %%i in ('dir /b "%PUBLISH_DIR%\*.*" 2^>nul ^| find /c /v ""') do echo ✅ Total files: %%i
) else (
    echo ❌ Publish directory not found: %PUBLISH_DIR%
)

echo.
echo [5/5] Summary...
echo.

echo ========================================
echo TEST RESULTS SUMMARY
echo ========================================
echo.

if exist "LSB.LogMonitor.Service.Setup\bin\Release\LSB.LogMonitor.Service.Setup.msi" (
    echo ✅ INSTALLER BUILD: SUCCESS
    echo    Location: LSB.LogMonitor.Service.Setup\bin\Release\LSB.LogMonitor.Service.Setup.msi
    
    for %%A in ("LSB.LogMonitor.Service.Setup\bin\Release\LSB.LogMonitor.Service.Setup.msi") do (
        if %%~zA GEQ 5000000 (
            echo ✅ INSTALLER SIZE: Good (%%~zA bytes^)
        ) else (
            echo ⚠️  INSTALLER SIZE: Small (%%~zA bytes^) - may be missing DLLs
        )
    )
) else (
    echo ❌ INSTALLER BUILD: FAILED
)

if exist "LSB.LogMonitor.Service.Setup\ServiceFiles.wxs" (
    echo ✅ HEAT GENERATION: SUCCESS
    for /f %%i in ('findstr /c:"<Component" "LSB.LogMonitor.Service.Setup\ServiceFiles.wxs"') do (
        if %%i GEQ 20 (
            echo ✅ COMPONENT COUNT: Good (%%i components^)
        ) else (
            echo ⚠️  COMPONENT COUNT: Low (%%i components^) - may be missing DLLs
        )
    )
) else (
    echo ❌ HEAT GENERATION: FAILED
)

echo.
echo 🎯 NEXT STEPS:
echo.
echo 1. If all tests passed:
echo    • Your installer is ready for deployment
echo    • Run: LSB.LogMonitor.Service.Setup\bin\Release\LSB.LogMonitor.Service.Setup.msi
echo.
echo 2. If tests failed:
echo    • Check error messages above
echo    • Ensure all packages are restored: dotnet restore
echo    • Ensure WiX Toolset is installed
echo    • Try building in Visual Studio for better error messages
echo.
echo 3. To rebuild installer:
echo    • Run: LSB.LogMonitor.Service.Setup\build-installer.bat
echo    • Or build LSB.LogMonitor.Service.Setup project in Visual Studio
echo.

pause
