@echo off
REM Install the LSB Log Monitor Service as a Windows service

SET SERVICE_NAME=LSBLogMonitorService
SET DISPLAY_NAME=LSB Log Monitor Service
SET EXE_NAME=LSB.LogMonitor.Service.exe

REM Check for admin rights
NET SESSION >nul 2>&1
IF %ERRORLEVEL% NEQ 0 (
    echo Please run this script as Administrator.
    pause
    exit /b 1
)

REM Install the service
sc create "%SERVICE_NAME%" binPath= "%~dp0%EXE_NAME%" DisplayName= "%DISPLAY_NAME%" start= auto

REM Start the service
sc start "%SERVICE_NAME%"

echo Service installed and started.
pause
