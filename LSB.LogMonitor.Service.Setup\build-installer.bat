@echo off
echo ========================================
echo LSB Log Monitor Service Installer Build
echo ========================================
echo.

echo This script will build a complete installer with ALL DLLs automatically included.
echo.

echo [STEP 1] Cleaning previous builds...
if exist "bin" rmdir /s /q "bin"
if exist "obj" rmdir /s /q "obj"
if exist "ServiceFiles.wxs" del "ServiceFiles.wxs"
echo ✅ Cleaned previous builds

echo.
echo [STEP 2] Building service project...
msbuild "..\LSB.LogMonitor.Service\LSB.LogMonitor.Service.csproj" /p:Configuration=Release /v:minimal
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Service build failed
    pause
    exit /b 1
)
echo ✅ Service project built successfully

echo.
echo [STEP 3] Publishing service with all dependencies...
msbuild "..\LSB.LogMonitor.Service\LSB.LogMonitor.Service.csproj" /t:Publish /p:Configuration=Release /v:minimal
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Service publish failed
    pause
    exit /b 1
)
echo ✅ Service published with all dependencies

echo.
echo [STEP 4] Checking publish directory...
set PUBLISH_DIR=..\LSB.LogMonitor.Service\bin\Release\net8.0\publish
if not exist "%PUBLISH_DIR%" (
    echo ❌ Publish directory not found: %PUBLISH_DIR%
    pause
    exit /b 1
)

echo ✅ Publish directory found: %PUBLISH_DIR%
for /f %%i in ('dir /b "%PUBLISH_DIR%\*.dll" 2^>nul ^| find /c /v ""') do echo ✅ Found %%i DLL files
for /f %%i in ('dir /b "%PUBLISH_DIR%\*.exe" 2^>nul ^| find /c /v ""') do echo ✅ Found %%i EXE files

echo.
echo [STEP 5] Running Heat to harvest files...
heat dir "%PUBLISH_DIR%" -cg ServiceFiles -gg -scom -sreg -sfrag -srd -dr INSTALLFOLDER -t ServiceFiles.xslt -out ServiceFiles.wxs
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Heat failed to generate ServiceFiles.wxs
    echo.
    echo Make sure WiX Toolset is installed and in PATH
    echo Download from: https://wixtoolset.org/releases/
    pause
    exit /b 1
)
echo ✅ Heat generated ServiceFiles.wxs with all components

echo.
echo [STEP 6] Checking generated ServiceFiles.wxs...
if exist "ServiceFiles.wxs" (
    for %%A in ("ServiceFiles.wxs") do echo ✅ ServiceFiles.wxs created (%%~zA bytes)
    
    echo.
    echo Checking component count...
    for /f %%i in ('findstr /c:"<Component" ServiceFiles.wxs') do echo ✅ Generated %%i components
    for /f %%i in ('findstr /c:"<File" ServiceFiles.wxs') do echo ✅ Generated %%i file entries
) else (
    echo ❌ ServiceFiles.wxs not generated
    pause
    exit /b 1
)

echo.
echo [STEP 7] Building WiX installer...
msbuild "LSB.LogMonitor.Service.Setup.wixproj" /p:Configuration=Release /v:minimal
if %ERRORLEVEL% NEQ 0 (
    echo ❌ WiX installer build failed
    echo.
    echo Check error messages above.
    echo Make sure all WiX files are correct.
    pause
    exit /b 1
)
echo ✅ WiX installer built successfully

echo.
echo [STEP 8] Checking installer output...
if exist "bin\Release\LSB.LogMonitor.Service.Setup.msi" (
    echo ✅ MSI installer created successfully!
    for %%A in ("bin\Release\LSB.LogMonitor.Service.Setup.msi") do (
        echo    File: LSB.LogMonitor.Service.Setup.msi
        echo    Size: %%~zA bytes
        echo    Location: %CD%\bin\Release\
    )
) else (
    echo ❌ MSI installer not found
    echo Check build errors above
    pause
    exit /b 1
)

echo.
echo ========================================
echo BUILD COMPLETED SUCCESSFULLY!
echo ========================================
echo.

echo ✅ INSTALLER FEATURES:
echo    • Automatically includes ALL DLLs from publish directory
echo    • Heat tool ensures no missing dependencies
echo    • Start Menu and Desktop shortcuts
echo    • Preserves configuration files during upgrade
echo    • Registry entries for tracking installation
echo.

echo 📁 INSTALLER LOCATION:
echo    %CD%\bin\Release\LSB.LogMonitor.Service.Setup.msi
echo.

echo 🚀 DEPLOYMENT:
echo    1. Copy MSI file to target machine
echo    2. Run as Administrator
echo    3. Application will be installed to: C:\Program Files\LSB\LogMonitor\
echo    4. Shortcuts will be created in Start Menu and Desktop
echo.

echo 🎯 WHAT'S INCLUDED:
echo    • LSB.LogMonitor.Service.exe (main application)
echo    • All Microsoft.Extensions.* DLLs
echo    • System.Text.Json.dll
echo    • YamlDotNet.dll
echo    • Configuration files (appsettings.json)
echo    • Runtime configuration files
echo    • ALL other dependencies automatically detected by Heat
echo.

set /p choice="Would you like to test the installer now? (y/n): "
if /i "%choice%"=="y" (
    echo.
    echo Starting installer...
    start "" "bin\Release\LSB.LogMonitor.Service.Setup.msi"
) else (
    echo.
    echo You can install later by running:
    echo bin\Release\LSB.LogMonitor.Service.Setup.msi
)

echo.
pause
