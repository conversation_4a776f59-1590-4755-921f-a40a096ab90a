﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>dotnet-LSB.LogMonitor.Service-488b1010-379d-47d4-ab1d-1010ef25f1a1</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Scripts\**" />
    <Content Remove="Scripts\**" />
    <EmbeddedResource Remove="Scripts\**" />
    <None Remove="Scripts\**" />
  </ItemGroup>

  <ItemGroup>
    <None Include="..\LSB.LogMonitor.Service.Installer\LSB.LogMonitor.Service.Installer.wixproj" Link="LSB.LogMonitor.Service.Installer.wixproj" />
    <None Include="..\LSB.LogMonitor.Service.Installer\Product.wxs" Link="Product.wxs" />
  </ItemGroup>

  <ItemGroup>
    <!-- Core hosting and dependency injection -->
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.1" />

    <!-- Configuration -->
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="8.0.2" />

    <!-- Logging -->
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.2" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="8.0.1" />

    <!-- Options -->
    <PackageReference Include="Microsoft.Extensions.Options" Version="8.0.2" />
    <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="8.0.0" />

    <!-- HTTP Client -->
    <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.1" />

    <!-- System packages -->
    <PackageReference Include="System.Management" Version="8.0.0" />
    <PackageReference Include="System.Diagnostics.PerformanceCounter" Version="8.0.0" />
    <PackageReference Include="System.Text.Json" Version="9.0.5" />

    <!-- Third party -->
    <PackageReference Include="YamlDotNet" Version="16.3.0" />
  </ItemGroup>
</Project>
