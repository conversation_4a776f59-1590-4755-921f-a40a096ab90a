﻿<Project Sdk="Microsoft.NET.Sdk.Worker">
	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<UserSecretsId>dotnet-LSB.LogMonitor.Service-488b1010-379d-47d4-ab1d-1010ef25f1a1</UserSecretsId>
	</PropertyGroup>
	<ItemGroup>
		<PackageReference Include="Microsoft.Extensions.Hosting" Version="6.0.1" />
		<PackageReference Include="Microsoft.Extensions.Hosting.WindowsServices" Version="6.0.2" />
		<PackageReference Include="Microsoft.Extensions.Http" Version="6.0.0" />
		<!-- BỎ DÒNG NÀY -->
		<!-- <PackageReference Include="Microsoft.Extensions.Logging.EventLog" Version="6.0.0" /> -->
		<PackageReference Include="System.Management" Version="6.0.0" />
		<PackageReference Include="System.Diagnostics.PerformanceCounter" Version="6.0.1" />
		<PackageReference Include="System.Text.Json" Version="6.0.9" />
		<PackageReference Include="YamlDotNet" Version="13.7.1" />
	</ItemGroup>
	<ItemGroup>
		<None Update="Scripts\install-service.bat">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\uninstall-service.bat">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
	</ItemGroup>
</Project>