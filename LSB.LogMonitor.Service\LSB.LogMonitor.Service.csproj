﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>dotnet-LSB.LogMonitor.Service-488b1010-379d-47d4-ab1d-1010ef25f1a1</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Scripts\**" />
    <Content Remove="Scripts\**" />
    <EmbeddedResource Remove="Scripts\**" />
    <None Remove="Scripts\**" />
  </ItemGroup>

  <ItemGroup>
    <None Include="..\LSB.LogMonitor.Service.Installer\LSB.LogMonitor.Service.Installer.wixproj" Link="LSB.LogMonitor.Service.Installer.wixproj" />
    <None Include="..\LSB.LogMonitor.Service.Installer\Product.wxs" Link="Product.wxs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.1" />
    <PackageReference Include="System.Management" Version="8.0.0" />
    <PackageReference Include="System.Diagnostics.PerformanceCounter" Version="8.0.0" />
    <PackageReference Include="System.Text.Json" Version="9.0.5" />
    <PackageReference Include="YamlDotNet" Version="16.3.0" />
  </ItemGroup>
</Project>
