<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" InitialTargets="EnsureWixToolsetInstalled" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">x86</Platform>
    <ProductVersion>3.10</ProductVersion>
    <ProjectGuid>eebaf30a-1eb5-4f08-a509-34267c6a6d36</ProjectGuid>
    <SchemaVersion>2.0</SchemaVersion>
    <OutputName>LSB.LogMonitor.Service.Setup</OutputName>
    <OutputType>Package</OutputType>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x86' ">
    <OutputPath>bin\$(Configuration)\</OutputPath>
    <IntermediateOutputPath>obj\$(Configuration)\</IntermediateOutputPath>
    <DefineConstants>Debug</DefineConstants>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x86' ">
    <OutputPath>bin\$(Configuration)\</OutputPath>
    <IntermediateOutputPath>obj\$(Configuration)\</IntermediateOutputPath>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="Product.wxs" />
    <Compile Include="ServiceFiles.wxs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\LSB.LogMonitor.Service\LSB.LogMonitor.Service.csproj">
      <Name>LSB.LogMonitor.Service</Name>
      <Project>{e7ba0dfe-9fa0-4ccb-8686-be24020626a1}</Project>
      <Private>True</Private>
      <DoNotHarvest>True</DoNotHarvest>
      <RefProjectOutputGroups>Binaries;Content;Satellites</RefProjectOutputGroups>
      <RefTargetDir>INSTALLFOLDER</RefTargetDir>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="build.bat" />
    <Content Include="start_service.bat" />
  </ItemGroup>
  <Import Project="$(WixTargetsPath)" Condition=" '$(WixTargetsPath)' != '' " />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\WiX\v3.x\Wix.targets" Condition=" '$(WixTargetsPath)' == '' AND Exists('$(MSBuildExtensionsPath32)\Microsoft\WiX\v3.x\Wix.targets') " />
  <Target Name="EnsureWixToolsetInstalled" Condition=" '$(WixTargetsImported)' != 'true' ">
    <Error Text="The WiX Toolset v3.11 build tools must be installed to build this project. To download the WiX Toolset, see https://wixtoolset.org/releases/v3.11/stable" />
  </Target>

  <!-- Auto-publish service project before Heat -->
  <Target Name="PublishServiceProject" BeforeTargets="BeforeBuild">
    <Message Text="[VS BUILD] Publishing service project to ensure all dependencies are available..." Importance="high" />
    <MSBuild Projects="..\LSB.LogMonitor.Service\LSB.LogMonitor.Service.csproj"
             Targets="Publish"
             Properties="Configuration=$(Configuration);Platform=Any CPU" />
  </Target>

  <!-- Heat target to harvest service files -->
  <Target Name="BeforeBuild" DependsOnTargets="PublishServiceProject">
    <Message Text="[VS BUILD] Running Heat to harvest files from publish directory..." Importance="high" />
    <HeatDirectory OutputFile="ServiceFiles.wxs"
                   DirectoryRefId="INSTALLFOLDER"
                   ComponentGroupName="ServiceFiles"
                   Directory="..\LSB.LogMonitor.Service\bin\$(Configuration)\net8.0\publish"
                   KeepEmptyDirectories="false"
                   SuppressFragments="true"
                   SuppressRegistry="true"
                   SuppressRootDirectory="true"
                   AutogenerateGuids="true"
                   GenerateGuidsNow="true"
                   Transforms="ServiceFiles.xslt" />
    <Message Text="[VS BUILD] Heat completed. Generated ServiceFiles.wxs with all dependencies." Importance="high" />
  </Target>
</Project>