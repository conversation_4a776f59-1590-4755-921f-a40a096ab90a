<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">
	<?define LSB.LogMonitor.Service_TargetDir=D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\?>

	<Product Id="*"
			 Name="LSB Telemetry Service"
			 Language="1033"
			 Codepage="1252"
			 Version="1.0.0.0"
			 Manufacturer="LSB"
			 UpgradeCode="{EB8A2174-7977-45CB-8EDB-936FBC25378E}">
		<Package InstallerVersion="200"
				 Compressed="yes"
				 InstallScope="perMachine"
				 Platform="x64" />
		<Media Id="1" Cabinet="files.cab" EmbedCab="yes" />

		<!-- AUTO UPDATE SUPPORT -->
		<MajorUpgrade DowngradeErrorMessage="A newer version is already installed."
					  Schedule="afterInstallInitialize" />

		<Directory Id="TARGETDIR" Name="SourceDir">
			<Directory Id="ProgramFiles64Folder">
				<Directory Id="CompanyFolder" Name="LSB">
					<Directory Id="INSTALLFOLDER" Name="TelemetryService" />
				</Directory>
			</Directory>
		</Directory>

		<Feature Id="Complete" Level="1">
			<ComponentGroupRef Id="ServiceFiles" />
		</Feature>

		<!-- Custom Actions to fix service issues -->
		<CustomAction Id="SetServiceTimeout"
					  Directory="INSTALLFOLDER"
					  ExeCommand='reg add "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control" /v "ServicesPipeTimeout" /t REG_DWORD /d "180000" /f'
					  Execute="deferred"
					  Impersonate="no" />

		<CustomAction Id="SetServiceConfig"
					  Directory="INSTALLFOLDER"
					  ExeCommand='sc config LSBTelemetryService start= auto obj= LocalSystem'
					  Execute="deferred"
					  Impersonate="no" />

		<CustomAction Id="SetServiceFailureActions"
					  Directory="INSTALLFOLDER"
					  ExeCommand='sc failure LSBTelemetryService reset= 86400 actions= restart/30000/restart/60000/restart/60000'
					  Execute="deferred"
					  Impersonate="no" />

		<InstallExecuteSequence>
			<Custom Action="SetServiceTimeout" After="InstallServices">NOT Installed</Custom>
			<Custom Action="SetServiceConfig" After="SetServiceTimeout">NOT Installed</Custom>
			<Custom Action="SetServiceFailureActions" After="SetServiceConfig">NOT Installed</Custom>
		</InstallExecuteSequence>
	</Product>

	<!-- Service Files - Generated automatically from build output -->
	<Fragment>
		<ComponentGroup Id="ServiceFiles" Directory="INSTALLFOLDER">
			<!-- Main Service Executable -->
			<Component Id="ServiceExe" Guid="{8CE3D205-31EE-415D-81C5-57FEDEB4B6A0}" Win64="yes">
				<File Id="ServiceExe"
					  Source="$(var.LSB.LogMonitor.Service_TargetDir)LSB.LogMonitor.Service.exe"
					  KeyPath="yes" />

				<!-- Service Installation -->
				<ServiceInstall Id="InstallService"
								Name="LSBTelemetryService"
								DisplayName="LSB Telemetry Service"
								Description="LSB Telemetry Service - System monitoring"
								Type="ownProcess"
								Start="auto"
								ErrorControl="normal"
								Account="LocalSystem" />

				<ServiceControl Id="ServiceControl"
								Name="LSBTelemetryService"
								Stop="both"
								Remove="uninstall"
								Wait="yes" />
			</Component>
			<!-- LSB.LogMonitor.Service.deps.json -->
			<Component Id="LSBLogMonitorServicedepsjson" Guid="{9A659D45-9AF3-4B15-BABD-5104DCB9C0F1}" Win64="yes">
				<File Id="LSBLogMonitorServicedepsjson" Source="$(var.LSB.LogMonitor.Service_TargetDir)LSB.LogMonitor.Service.deps.json" />
			</Component>
			<!-- LSB.LogMonitor.Service.runtimeconfig.json -->
			<Component Id="LSBLogMonitorServiceruntimeconfigjson" Guid="{3D9F0C37-928E-49F1-A95D-EC165D776FF2}" Win64="yes">
				<File Id="LSBLogMonitorServiceruntimeconfigjson" Source="$(var.LSB.LogMonitor.Service_TargetDir)LSB.LogMonitor.Service.runtimeconfig.json" />
			</Component>
			<!-- LSB.LogMonitor.Service.pdb -->
			<Component Id="LSBLogMonitorServicepdb" Guid="{525894B9-440A-4F7F-B4E6-A21816A22AEE}" Win64="yes">
				<File Id="LSBLogMonitorServicepdb" Source="$(var.LSB.LogMonitor.Service_TargetDir)LSB.LogMonitor.Service.pdb" />
			</Component>
			<!-- appsettings.json -->
			<Component Id="appsettingsjson" Guid="{848F0633-58EF-40D6-AB4E-A41380B24E20}" Win64="yes">
				<File Id="appsettingsjson" Source="$(var.LSB.LogMonitor.Service_TargetDir)appsettings.json" />
			</Component>
			<!-- appsettings.Development.json -->
			<Component Id="appsettingsDevelopmentjson" Guid="{AFC38A06-16C6-4D0A-91C9-AE7B0BB2C883}" Win64="yes">
				<File Id="appsettingsDevelopmentjson" Source="$(var.LSB.LogMonitor.Service_TargetDir)appsettings.Development.json" />
			</Component>
			<!-- LSB.LogMonitor.Service.dll -->
			<Component Id="LSBLogMonitorServicedll" Guid="{68E1223C-A55B-4A86-9857-C2C19F16DC9A}" Win64="yes">
				<File Id="LSBLogMonitorServicedll" Source="$(var.LSB.LogMonitor.Service_TargetDir)LSB.LogMonitor.Service.dll" />
			</Component>
		</ComponentGroup>
	</Fragment>
</Wix>
