# 🎯 LSB Log Monitor Service - WiX Installer với Heat Tool

## ✅ **Installer tự động bao gồm TẤT CẢ DLL**

Installer này được thiết kế để **tự động thu thập tất cả DLL** cần thiết mỗi lần build, sử dụng WiX Heat tool.

---

## 🚀 **Cách build installer:**

### **Quick Start:**
```cmd
# Test và build installer
test-installer.bat

# Hoặc chỉ build
cd LSB.LogMonitor.Service.Setup
build-installer.bat
```

### **Visual Studio:**
1. **Open Visual Studio**
2. **Open LSB.LogMonitor.Service.sln**
3. **Right-click LSB.LogMonitor.Service.Setup project**
4. **Build**

---

## 🔧 **Cách hoạt động:**

### **Automatic Workflow:**
```mermaid
graph TD
    A[Build Service Project] --> B[Publish with Dependencies]
    B --> C[Heat Scans Publish Directory]
    C --> D[Generate ServiceFiles.wxs]
    D --> E[XSLT Transform]
    E --> F[WiX Compile]
    F --> G[MSI Installer]
```

### **Files quan trọng:**
- **`Product.wxs`** - Installer configuration (có thể edit)
- **`ServiceFiles.xslt`** - Heat transform rules (có thể edit)
- **`ServiceFiles.wxs`** - Generated by Heat (KHÔNG edit)
- **`LSB.LogMonitor.Service.Setup.wixproj`** - Build configuration

---

## 📁 **Installer Features:**

### **✅ Tự động bao gồm:**
- **LSB.LogMonitor.Service.exe** - Main application
- **Microsoft.Extensions.*** - Tất cả hosting/DI/logging DLLs
- **System.Text.Json.dll** - JSON processing
- **YamlDotNet.dll** - YAML processing
- **appsettings.json** - Configuration (preserved during upgrade)
- **Runtime configs** - .deps.json, .runtimeconfig.json
- **TẤT CẢ dependencies khác** - Automatically detected

### **✅ Installer tạo:**
- **Start Menu shortcut** - "LSB Log Monitor"
- **Desktop shortcut** - "LSB Log Monitor"
- **Registry entries** - Installation tracking
- **Install directory** - `C:\Program Files\LSB\LogMonitor\`

### **✅ Upgrade support:**
- **Preserves config files** - appsettings.json không bị overwrite
- **Automatic uninstall** - Old version removed before new install
- **Registry cleanup** - Proper uninstall process

---

## 🎯 **Build Process:**

### **Step 1: Service Build**
```cmd
msbuild LSB.LogMonitor.Service.csproj /p:Configuration=Release
```

### **Step 2: Publish with Dependencies**
```cmd
msbuild LSB.LogMonitor.Service.csproj /t:Publish /p:Configuration=Release
```

### **Step 3: Heat Harvesting**
```cmd
heat dir "publish" -cg ServiceFiles -gg -scom -sreg -sfrag -srd -dr INSTALLFOLDER -t ServiceFiles.xslt -out ServiceFiles.wxs
```

### **Step 4: WiX Compile**
```cmd
msbuild LSB.LogMonitor.Service.Setup.wixproj /p:Configuration=Release
```

---

## 🔧 **Customization:**

### **Thay đổi installer info (Product.wxs):**
```xml
<Product Id="*"
         Name="LSB Log Monitor Service"           <!-- Tên hiển thị -->
         Version="1.0.0.0"                       <!-- Version -->
         Manufacturer="LSB"                       <!-- Nhà sản xuất -->
         UpgradeCode="{EB8A2174-7977-45CB-8EDB-936FBC25378E}">
```

### **Thay đổi install directory:**
```xml
<Directory Id="INSTALLFOLDER" Name="LogMonitor" />  <!-- Thay đổi tên thư mục -->
```

### **Thêm/bỏ shortcuts:**
```xml
<!-- Bỏ desktop shortcut -->
<!-- <ComponentRef Id="DesktopShortcut" /> -->

<!-- Thêm custom shortcut -->
<Component Id="CustomShortcut" Directory="ApplicationProgramsFolder" Guid="*">
    <Shortcut Id="CustomShortcut" Name="Custom Name" Target="[INSTALLFOLDER]LSB.LogMonitor.Service.exe" />
</Component>
```

### **Customize Heat behavior (ServiceFiles.xslt):**
```xml
<!-- Preserve custom config files -->
<xsl:template match="wix:Component[wix:File[@Source and contains(@Source, '.config')]]">
  <xsl:copy>
    <xsl:apply-templates select="@*"/>
    <wix:File>
      <xsl:apply-templates select="wix:File/@*"/>
      <xsl:attribute name="NeverOverwrite">yes</xsl:attribute>
    </wix:File>
  </xsl:copy>
</xsl:template>
```

---

## 🚨 **Troubleshooting:**

### **❌ "Heat not found"**
**Solution:**
1. Install WiX Toolset: https://wixtoolset.org/releases/
2. Add to PATH environment variable
3. Restart command prompt/Visual Studio

### **❌ "ServiceFiles.wxs not generated"**
**Solution:**
1. Check publish directory exists: `LSB.LogMonitor.Service\bin\Release\net8.0\publish`
2. Ensure Heat command runs successfully
3. Check Heat parameters in .wixproj file

### **❌ "Missing DLLs in installer"**
**Solution:**
1. Check publish directory has all DLLs: `dir publish\*.dll`
2. Verify Heat includes all files: `findstr "\.dll" ServiceFiles.wxs`
3. Ensure project file has all PackageReference entries

### **❌ "Build failed in Visual Studio"**
**Solution:**
1. Check Output window for detailed errors
2. Ensure WiX Toolset Visual Studio extension installed
3. Try building from command line for better error messages

---

## 📊 **Verification:**

### **Check installer includes all DLLs:**
```cmd
# Run test script
test-installer.bat

# Manual check
findstr /i "Microsoft.Extensions" LSB.LogMonitor.Service.Setup\ServiceFiles.wxs
findstr /i "System.Text.Json" LSB.LogMonitor.Service.Setup\ServiceFiles.wxs
findstr /i "YamlDotNet" LSB.LogMonitor.Service.Setup\ServiceFiles.wxs
```

### **Expected results:**
- **MSI file size**: >5MB (includes all DLLs)
- **Component count**: >20 components
- **Key DLLs present**: Microsoft.Extensions.*, System.Text.Json, YamlDotNet

---

## 🎉 **Deployment:**

### **Install:**
```cmd
# Run as Administrator
LSB.LogMonitor.Service.Setup.msi

# Silent install
msiexec /i LSB.LogMonitor.Service.Setup.msi /quiet

# Install with logging
msiexec /i LSB.LogMonitor.Service.Setup.msi /l*v install.log
```

### **Uninstall:**
```cmd
# Through Control Panel
Programs and Features > LSB Log Monitor Service > Uninstall

# Command line
msiexec /x LSB.LogMonitor.Service.Setup.msi /quiet
```

### **Verify installation:**
```cmd
# Check files installed
dir "C:\Program Files\LSB\LogMonitor"

# Check shortcuts created
dir "%USERPROFILE%\Desktop\LSB Log Monitor.lnk"
dir "%PROGRAMDATA%\Microsoft\Windows\Start Menu\Programs\LSB Log Monitor"

# Test application
"C:\Program Files\LSB\LogMonitor\LSB.LogMonitor.Service.exe" --test
```

---

## ✅ **Summary:**

**Heat tool đảm bảo:**
- ✅ **Tự động thu thập TẤT CẢ DLL** từ publish directory
- ✅ **Không bỏ sót dependencies** nào
- ✅ **Không cần maintain manual list** của DLLs
- ✅ **Tự động update** khi thêm packages mới
- ✅ **Consistent builds** mỗi lần

**Chỉ cần chạy `test-installer.bat` và bạn có installer hoàn chỉnh với tất cả DLLs!** 🎉
