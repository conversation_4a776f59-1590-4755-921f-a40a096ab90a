# Generate WiX Components from Build Output
# This script scans the build output and generates WiX component definitions

param(
    [Parameter()]
    [string]$TargetDir = "D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0",
    
    [Parameter()]
    [string]$OutputFile = "ServiceFiles_Generated.wxs"
)

Write-Host "Generating WiX Components from: $TargetDir" -ForegroundColor Green
Write-Host "Output file: $OutputFile" -ForegroundColor Green
Write-Host ""

if (-not (Test-Path $TargetDir)) {
    Write-Host "Error: Target directory not found: $TargetDir" -ForegroundColor Red
    Write-Host "Please build the project first." -ForegroundColor Red
    exit 1
}

# Get all DLL files
$dllFiles = Get-ChildItem -Path $TargetDir -Filter "*.dll" | Sort-Object Name

# Get other important files
$otherFiles = @(
    "LSB.LogMonitor.Service.exe",
    "LSB.LogMonitor.Service.deps.json", 
    "LSB.LogMonitor.Service.runtimeconfig.json",
    "LSB.LogMonitor.Service.pdb",
    "appsettings.json",
    "appsettings.Development.json"
)

Write-Host "Found $($dllFiles.Count) DLL files" -ForegroundColor Yellow
Write-Host "Found $($otherFiles.Count) other files" -ForegroundColor Yellow
Write-Host ""

# Generate GUID function
function New-Guid {
    return [System.Guid]::NewGuid().ToString().ToUpper()
}

# Generate component ID from filename
function Get-ComponentId {
    param([string]$fileName)
    $cleanName = $fileName -replace '\.', '' -replace '-', '' -replace '_', ''
    return $cleanName
}

# Start generating the WiX file
$wixContent = @"
<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">
    <Fragment>
        <ComponentGroup Id="ServiceFiles" Directory="INSTALLFOLDER">
"@

# Add service executable first
$serviceExeGuid = New-Guid
$wixContent += @"

            <!-- Main Service Executable -->
            <Component Id="ServiceExe" Guid="{$serviceExeGuid}" Win64="yes">
                <File Id="ServiceExe"
                      Source="`$(var.LSB.LogMonitor.Service_TargetDir)LSB.LogMonitor.Service.exe"
                      KeyPath="yes" />

                <!-- Service Installation -->
                <ServiceInstall Id="InstallService"
                                Name="LSBTelemetryService"
                                DisplayName="LSB Telemetry Service"
                                Description="LSB Telemetry Service - System monitoring"
                                Type="ownProcess"
                                Start="auto"
                                ErrorControl="normal"
                                Account="LocalSystem" />

                <ServiceControl Id="ServiceControl"
                                Name="LSBTelemetryService"
                                Stop="both"
                                Remove="uninstall"
                                Wait="yes" />
            </Component>
"@

# Add other important files
foreach ($file in $otherFiles) {
    if ($file -eq "LSB.LogMonitor.Service.exe") { continue } # Already added above
    
    $filePath = Join-Path $TargetDir $file
    if (Test-Path $filePath) {
        $componentId = Get-ComponentId $file
        $fileId = $componentId
        $guid = New-Guid
        
        $wixContent += @"

            <!-- $file -->
            <Component Id="$componentId" Guid="{$guid}" Win64="yes">
                <File Id="$fileId" Source="`$(var.LSB.LogMonitor.Service_TargetDir)$file" />
            </Component>
"@
    }
}

# Add DLL files
foreach ($dll in $dllFiles) {
    $componentId = Get-ComponentId $dll.Name
    $fileId = $componentId
    $guid = New-Guid
    
    $wixContent += @"

            <!-- $($dll.Name) -->
            <Component Id="$componentId" Guid="{$guid}" Win64="yes">
                <File Id="$fileId" Source="`$(var.LSB.LogMonitor.Service_TargetDir)$($dll.Name)" />
            </Component>
"@
}

# Close the WiX file
$wixContent += @"

        </ComponentGroup>
    </Fragment>
</Wix>
"@

# Write to file
$wixContent | Out-File -FilePath $OutputFile -Encoding UTF8

Write-Host "Generated WiX components file: $OutputFile" -ForegroundColor Green
Write-Host ""
Write-Host "Summary:" -ForegroundColor Cyan
Write-Host "- Service executable: LSB.LogMonitor.Service.exe" -ForegroundColor White
Write-Host "- Configuration files: $($otherFiles.Count - 1)" -ForegroundColor White
Write-Host "- DLL dependencies: $($dllFiles.Count)" -ForegroundColor White
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Review the generated file: $OutputFile" -ForegroundColor White
Write-Host "2. Replace the ServiceFiles ComponentGroup in Product.wxs" -ForegroundColor White
Write-Host "3. Build the installer project" -ForegroundColor White
