<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">
	<?define LSB.LogMonitor.Service_TargetDir=D:\CodeBase\LSB.LogMonitor.Service\LSB.LogMonitor.Service\bin\Release\net8.0\publish\?>

	<Product Id="*"
			 Name="LSB Telemetry Service"
			 Language="1033"
			 Codepage="1252"
			 Version="1.0.0.0"
			 Manufacturer="LSB"
			 UpgradeCode="{EB8A2174-7977-45CB-8EDB-936FBC25378E}">
		<Package InstallerVersion="200"
				 Compressed="yes"
				 InstallScope="perMachine"
				 Platform="x64" />
		<Media Id="1" Cabinet="files.cab" EmbedCab="yes" />

		<!-- AUTO UPDATE SUPPORT -->
		<MajorUpgrade DowngradeErrorMessage="A newer version is already installed."
					  Schedule="afterInstallInitialize" />

		<Directory Id="TARGETDIR" Name="SourceDir">
			<Directory Id="ProgramFiles64Folder">
				<Directory Id="CompanyFolder" Name="LSB">
					<Directory Id="INSTALLFOLDER" Name="TelemetryService" />
				</Directory>
			</Directory>
		</Directory>

		<Feature Id="Complete" Level="1">
			<ComponentGroupRef Id="ServiceFiles" />
		</Feature>

		<!-- Custom Actions to fix service issues -->
		<CustomAction Id="SetServiceTimeout"
					  Directory="INSTALLFOLDER"
					  ExeCommand='reg add "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control" /v "ServicesPipeTimeout" /t REG_DWORD /d "180000" /f'
					  Execute="deferred"
					  Impersonate="no" />

		<CustomAction Id="SetServiceConfig"
					  Directory="INSTALLFOLDER"
					  ExeCommand='sc config LSBTelemetryService start= auto obj= LocalSystem'
					  Execute="deferred"
					  Impersonate="no" />

		<CustomAction Id="SetServiceFailureActions"
					  Directory="INSTALLFOLDER"
					  ExeCommand='sc failure LSBTelemetryService reset= 86400 actions= restart/30000/restart/60000/restart/60000'
					  Execute="deferred"
					  Impersonate="no" />

		<InstallExecuteSequence>
			<Custom Action="SetServiceTimeout" After="InstallServices">NOT Installed</Custom>
			<Custom Action="SetServiceConfig" After="SetServiceTimeout">NOT Installed</Custom>
			<Custom Action="SetServiceFailureActions" After="SetServiceConfig">NOT Installed</Custom>
		</InstallExecuteSequence>
	</Product>

	<!-- Service Files - Generated automatically from build output -->
	<Fragment>
		<ComponentGroup Id="ServiceFiles" Directory="INSTALLFOLDER">
			<!-- Main Service Executable -->
			<Component Id="ServiceExe" Guid="{8CE3D205-31EE-415D-81C5-57FEDEB4B6A0}" Win64="yes">
				<File Id="ServiceExe"
					  Source="$(var.LSB.LogMonitor.Service_TargetDir)LSB.LogMonitor.Service.exe"
					  KeyPath="yes" />

				<!-- Service Installation -->
				<ServiceInstall Id="InstallService"
								Name="LSBTelemetryService"
								DisplayName="LSB Telemetry Service"
								Description="LSB Telemetry Service - System monitoring"
								Type="ownProcess"
								Start="auto"
								ErrorControl="normal"
								Account="LocalSystem" />

				<ServiceControl Id="ServiceControl"
								Name="LSBTelemetryService"
								Stop="both"
								Remove="uninstall"
								Wait="yes" />
			</Component>
			<!-- LSB.LogMonitor.Service.deps.json -->
			<Component Id="LSBLogMonitorServicedepsjson" Guid="{9A659D45-9AF3-4B15-BABD-5104DCB9C0F1}" Win64="yes">
				<File Id="LSBLogMonitorServicedepsjson" Source="$(var.LSB.LogMonitor.Service_TargetDir)LSB.LogMonitor.Service.deps.json" />
			</Component>
			<!-- LSB.LogMonitor.Service.runtimeconfig.json -->
			<Component Id="LSBLogMonitorServiceruntimeconfigjson" Guid="{3D9F0C37-928E-49F1-A95D-EC165D776FF2}" Win64="yes">
				<File Id="LSBLogMonitorServiceruntimeconfigjson" Source="$(var.LSB.LogMonitor.Service_TargetDir)LSB.LogMonitor.Service.runtimeconfig.json" />
			</Component>
			<!-- LSB.LogMonitor.Service.pdb -->
			<Component Id="LSBLogMonitorServicepdb" Guid="{525894B9-440A-4F7F-B4E6-A21816A22AEE}" Win64="yes">
				<File Id="LSBLogMonitorServicepdb" Source="$(var.LSB.LogMonitor.Service_TargetDir)LSB.LogMonitor.Service.pdb" />
			</Component>
			<!-- appsettings.json -->
			<Component Id="appsettingsjson" Guid="{848F0633-58EF-40D6-AB4E-A41380B24E20}" Win64="yes">
				<File Id="appsettingsjson" Source="$(var.LSB.LogMonitor.Service_TargetDir)appsettings.json" />
			</Component>
			<!-- appsettings.Development.json -->
			<Component Id="appsettingsDevelopmentjson" Guid="{AFC38A06-16C6-4D0A-91C9-AE7B0BB2C883}" Win64="yes">
				<File Id="appsettingsDevelopmentjson" Source="$(var.LSB.LogMonitor.Service_TargetDir)appsettings.Development.json" />
			</Component>
			<!-- LSB.LogMonitor.Service.dll -->
			<Component Id="LSBLogMonitorServicedll" Guid="{68E1223C-A55B-4A86-9857-C2C19F16DC9A}" Win64="yes">
				<File Id="LSBLogMonitorServicedll" Source="$(var.LSB.LogMonitor.Service_TargetDir)LSB.LogMonitor.Service.dll" />
			</Component>
			<!-- Microsoft.Extensions.Configuration.Abstractions.dll -->
			<Component Id="MicrosoftExtensionsConfigurationAbstractionsdll" Guid="{0E818EDD-A484-4265-8BF3-BBBEC74472C5}" Win64="yes">
				<File Id="MicrosoftExtensionsConfigurationAbstractionsdll" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.Configuration.Abstractions.dll" />
			</Component>
			<!-- Microsoft.Extensions.Configuration.Binder.dll -->
			<Component Id="MicrosoftExtensionsConfigurationBinderdll" Guid="{F3B862F2-5736-4FD0-A1F1-00D0185A2CD1}" Win64="yes">
				<File Id="MicrosoftExtensionsConfigurationBinderdll" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.Configuration.Binder.dll" />
			</Component>
			<!-- Microsoft.Extensions.Configuration.CommandLine.dll -->
			<Component Id="MicrosoftExtensionsConfigurationCommandLinedll" Guid="{341A63AC-E3D7-4EEA-A6FE-B3D7420E822E}" Win64="yes">
				<File Id="MicrosoftExtensionsConfigurationCommandLinedll" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.Configuration.CommandLine.dll" />
			</Component>
			<!-- Microsoft.Extensions.Configuration.dll -->
			<Component Id="MicrosoftExtensionsConfigurationdll" Guid="{86F32E48-0C81-4862-954C-C8339E445BAA}" Win64="yes">
				<File Id="MicrosoftExtensionsConfigurationdll" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.Configuration.dll" />
			</Component>
			<!-- Microsoft.Extensions.Configuration.EnvironmentVariables.dll -->
			<Component Id="MicrosoftExtensionsConfigurationEnvironmentVariablesdll" Guid="{E25D2ABE-6159-48F0-A19F-98B37A30CBF1}" Win64="yes">
				<File Id="MicrosoftExtensionsConfigurationEnvironmentVariablesdll" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.Configuration.EnvironmentVariables.dll" />
			</Component>
			<!-- Microsoft.Extensions.Configuration.FileExtensions.dll -->
			<Component Id="MicrosoftExtensionsConfigurationFileExtensionsdll" Guid="{23678AB0-BAE8-4A97-AA87-6176276BE472}" Win64="yes">
				<File Id="MicrosoftExtensionsConfigurationFileExtensionsdll" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.Configuration.FileExtensions.dll" />
			</Component>
			<!-- Microsoft.Extensions.Configuration.Json.dll -->
			<Component Id="MicrosoftExtensionsConfigurationJsondll" Guid="{8B2CAD49-BB44-4D68-9D01-467AA5AF6924}" Win64="yes">
				<File Id="MicrosoftExtensionsConfigurationJsondll" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.Configuration.Json.dll" />
			</Component>
			<!-- Microsoft.Extensions.Configuration.UserSecrets.dll -->
			<Component Id="MicrosoftExtensionsConfigurationUserSecretsdll" Guid="{1B8EDBCE-32AE-479C-9B1C-ABEA446DE35A}" Win64="yes">
				<File Id="MicrosoftExtensionsConfigurationUserSecretsdll" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.Configuration.UserSecrets.dll" />
			</Component>
			<!-- Microsoft.Extensions.DependencyInjection.Abstractions.dll -->
			<Component Id="MicrosoftExtensionsDependencyInjectionAbstractionsdll" Guid="{F1F3EF1B-B361-4438-975E-B2CFF01D0C7A}" Win64="yes">
				<File Id="MicrosoftExtensionsDependencyInjectionAbstractionsdll" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.DependencyInjection.Abstractions.dll" />
			</Component>
			<!-- Microsoft.Extensions.DependencyInjection.dll -->
			<Component Id="MicrosoftExtensionsDependencyInjectiondll" Guid="{E8AEEE64-EC22-4469-9EEC-07176FE02D44}" Win64="yes">
				<File Id="MicrosoftExtensionsDependencyInjectiondll" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.DependencyInjection.dll" />
			</Component>
			<!-- Microsoft.Extensions.Diagnostics.Abstractions.dll -->
			<Component Id="MicrosoftExtensionsDiagnosticsAbstractionsdll" Guid="{D140B3AE-E543-4542-A782-4B30D5000A63}" Win64="yes">
				<File Id="MicrosoftExtensionsDiagnosticsAbstractionsdll" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.Diagnostics.Abstractions.dll" />
			</Component>
			<!-- Microsoft.Extensions.Diagnostics.dll -->
			<Component Id="MicrosoftExtensionsDiagnosticsdll" Guid="{5DFF7A20-228D-41D2-8E89-1F038E8AC67D}" Win64="yes">
				<File Id="MicrosoftExtensionsDiagnosticsdll" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.Diagnostics.dll" />
			</Component>
			<!-- Microsoft.Extensions.FileProviders.Abstractions.dll -->
			<Component Id="MicrosoftExtensionsFileProvidersAbstractionsdll" Guid="{DE6001D9-D751-436A-9059-1B5C1FE39728}" Win64="yes">
				<File Id="MicrosoftExtensionsFileProvidersAbstractionsdll" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.FileProviders.Abstractions.dll" />
			</Component>
			<!-- Microsoft.Extensions.FileProviders.Physical.dll -->
			<Component Id="MicrosoftExtensionsFileProvidersPhysicaldll" Guid="{D9E437EE-38C9-4977-AED6-7BC95C56B28E}" Win64="yes">
				<File Id="MicrosoftExtensionsFileProvidersPhysicaldll" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.FileProviders.Physical.dll" />
			</Component>
			<!-- Microsoft.Extensions.FileSystemGlobbing.dll -->
			<Component Id="MicrosoftExtensionsFileSystemGlobbingdll" Guid="{8013191F-6A56-4C9D-B967-6353E672207F}" Win64="yes">
				<File Id="MicrosoftExtensionsFileSystemGlobbingdll" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.FileSystemGlobbing.dll" />
			</Component>
			<!-- Microsoft.Extensions.Hosting.Abstractions.dll -->
			<Component Id="MicrosoftExtensionsHostingAbstractionsdll" Guid="{3EA3AACF-6060-45BE-B2ED-9BCD396CEB09}" Win64="yes">
				<File Id="MicrosoftExtensionsHostingAbstractionsdll" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.Hosting.Abstractions.dll" />
			</Component>
			<!-- Microsoft.Extensions.Hosting.dll -->
			<Component Id="MicrosoftExtensionsHostingdll" Guid="{9471016F-65CC-438B-9FC6-BB6BE965320C}" Win64="yes">
				<File Id="MicrosoftExtensionsHostingdll" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.Hosting.dll" />
			</Component>
			<!-- Microsoft.Extensions.Hosting.WindowsServices.dll -->
			<Component Id="MicrosoftExtensionsHostingWindowsServicesdll" Guid="{EAB556B6-E086-4EF8-92DE-138BA836DC6F}" Win64="yes">
				<File Id="MicrosoftExtensionsHostingWindowsServicesdll" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.Hosting.WindowsServices.dll" />
			</Component>
			<!-- Microsoft.Extensions.Http.dll -->
			<Component Id="MicrosoftExtensionsHttpdll" Guid="{********-106D-4A88-993F-2E7BEECE19B3}" Win64="yes">
				<File Id="MicrosoftExtensionsHttpdll" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.Http.dll" />
			</Component>
			<!-- Microsoft.Extensions.Logging.Abstractions.dll -->
			<Component Id="MicrosoftExtensionsLoggingAbstractionsdll" Guid="{A46B2EB9-3E0F-4B8F-BD18-4D10C976A012}" Win64="yes">
				<File Id="MicrosoftExtensionsLoggingAbstractionsdll" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.Logging.Abstractions.dll" />
			</Component>
			<!-- Microsoft.Extensions.Logging.Configuration.dll -->
			<Component Id="MicrosoftExtensionsLoggingConfigurationdll" Guid="{6C4C943A-6DCE-429F-91E6-4BBDCF718180}" Win64="yes">
				<File Id="MicrosoftExtensionsLoggingConfigurationdll" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.Logging.Configuration.dll" />
			</Component>
			<!-- Microsoft.Extensions.Logging.Console.dll -->
			<Component Id="MicrosoftExtensionsLoggingConsoledll" Guid="{0826C5CE-3AA2-484E-8396-4EB616790E49}" Win64="yes">
				<File Id="MicrosoftExtensionsLoggingConsoledll" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.Logging.Console.dll" />
			</Component>
			<!-- Microsoft.Extensions.Logging.Debug.dll -->
			<Component Id="MicrosoftExtensionsLoggingDebugdll" Guid="{D54B24B5-E152-4EC9-AE03-8B88F7685854}" Win64="yes">
				<File Id="MicrosoftExtensionsLoggingDebugdll" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.Logging.Debug.dll" />
			</Component>
			<!-- Microsoft.Extensions.Logging.dll -->
			<Component Id="MicrosoftExtensionsLoggingdll" Guid="{C554B085-9CEC-43F9-A26F-59E608B5FABB}" Win64="yes">
				<File Id="MicrosoftExtensionsLoggingdll" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.Logging.dll" />
			</Component>
			<!-- Microsoft.Extensions.Logging.EventLog.dll -->
			<Component Id="MicrosoftExtensionsLoggingEventLogdll" Guid="{DFC5F360-C117-470B-AAF6-971646CDB696}" Win64="yes">
				<File Id="MicrosoftExtensionsLoggingEventLogdll" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.Logging.EventLog.dll" />
			</Component>
			<!-- Microsoft.Extensions.Logging.EventSource.dll -->
			<Component Id="MicrosoftExtensionsLoggingEventSourcedll" Guid="{696797FA-4B15-41F1-AA8D-03FC19E9EE77}" Win64="yes">
				<File Id="MicrosoftExtensionsLoggingEventSourcedll" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.Logging.EventSource.dll" />
			</Component>
			<!-- Microsoft.Extensions.Options.ConfigurationExtensions.dll -->
			<Component Id="MicrosoftExtensionsOptionsConfigurationExtensionsdll" Guid="{0D68C4CB-1B4C-48A4-BA71-1D9C0010304B}" Win64="yes">
				<File Id="MicrosoftExtensionsOptionsConfigurationExtensionsdll" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.Options.ConfigurationExtensions.dll" />
			</Component>
			<!-- Microsoft.Extensions.Options.dll -->
			<Component Id="MicrosoftExtensionsOptionsdll" Guid="{ABF5D288-B8C2-4F1E-B9FE-6BAE80DD1306}" Win64="yes">
				<File Id="MicrosoftExtensionsOptionsdll" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.Options.dll" />
			</Component>
			<!-- Microsoft.Extensions.Primitives.dll -->
			<Component Id="MicrosoftExtensionsPrimitivesdll" Guid="{CEFFAA3D-91D2-46FF-B2E9-F82E0F573902}" Win64="yes">
				<File Id="MicrosoftExtensionsPrimitivesdll" Source="$(var.LSB.LogMonitor.Service_TargetDir)Microsoft.Extensions.Primitives.dll" />
			</Component>
			<!-- System.CodeDom.dll -->
			<Component Id="SystemCodeDomdll" Guid="{12788CB1-D97E-498A-B776-53C4FCF85310}" Win64="yes">
				<File Id="SystemCodeDomdll" Source="$(var.LSB.LogMonitor.Service_TargetDir)System.CodeDom.dll" />
			</Component>
			<!-- System.Configuration.ConfigurationManager.dll -->
			<Component Id="SystemConfigurationConfigurationManagerdll" Guid="{572AB4F2-8F70-4717-AC9D-FCD1BE2F00A5}" Win64="yes">
				<File Id="SystemConfigurationConfigurationManagerdll" Source="$(var.LSB.LogMonitor.Service_TargetDir)System.Configuration.ConfigurationManager.dll" />
			</Component>
			<!-- System.Diagnostics.EventLog.dll -->
			<Component Id="SystemDiagnosticsEventLogdll" Guid="{771DB305-AB86-44C7-B7BC-CC77E5F91CEF}" Win64="yes">
				<File Id="SystemDiagnosticsEventLogdll" Source="$(var.LSB.LogMonitor.Service_TargetDir)System.Diagnostics.EventLog.dll" />
			</Component>
			<!-- System.Diagnostics.EventLog.Messages.dll -->
			<Component Id="SystemDiagnosticsEventLogMessagesdll" Guid="{881DB305-AB86-44C7-B7BC-CC77E5F91CEF}" Win64="yes">
				<File Id="SystemDiagnosticsEventLogMessagesdll" Source="$(var.LSB.LogMonitor.Service_TargetDir)System.Diagnostics.EventLog.Messages.dll" />
			</Component>
			<!-- System.Diagnostics.PerformanceCounter.dll -->
			<Component Id="SystemDiagnosticsPerformanceCounterdll" Guid="{E04EB193-F442-4A81-83F3-6F470C079C99}" Win64="yes">
				<File Id="SystemDiagnosticsPerformanceCounterdll" Source="$(var.LSB.LogMonitor.Service_TargetDir)System.Diagnostics.PerformanceCounter.dll" />
			</Component>
			<!-- System.IO.Pipelines.dll -->
			<Component Id="SystemIOPipelinesdll" Guid="{********-B415-4642-B670-5EB30A28FD8D}" Win64="yes">
				<File Id="SystemIOPipelinesdll" Source="$(var.LSB.LogMonitor.Service_TargetDir)System.IO.Pipelines.dll" />
			</Component>
			<!-- System.Management.dll -->
			<Component Id="SystemManagementdll" Guid="{37C1F26E-A849-45C6-9F5B-574C6FEA4CED}" Win64="yes">
				<File Id="SystemManagementdll" Source="$(var.LSB.LogMonitor.Service_TargetDir)System.Management.dll" />
			</Component>
			<!-- System.Security.Cryptography.ProtectedData.dll -->
			<Component Id="SystemSecurityCryptographyProtectedDatadll" Guid="{5B8284FC-9229-44E2-A641-08CF3873C8E2}" Win64="yes">
				<File Id="SystemSecurityCryptographyProtectedDatadll" Source="$(var.LSB.LogMonitor.Service_TargetDir)System.Security.Cryptography.ProtectedData.dll" />
			</Component>
			<!-- System.ServiceProcess.ServiceController.dll -->
			<Component Id="SystemServiceProcessServiceControllerdll" Guid="{BFD5A2BE-E07B-4711-A874-5A34CD247185}" Win64="yes">
				<File Id="SystemServiceProcessServiceControllerdll" Source="$(var.LSB.LogMonitor.Service_TargetDir)System.ServiceProcess.ServiceController.dll" />
			</Component>
			<!-- System.Text.Encodings.Web.dll -->
			<Component Id="SystemTextEncodingsWebdll" Guid="{F54583A1-9EAB-4185-BCB7-67DBECB619C8}" Win64="yes">
				<File Id="SystemTextEncodingsWebdll" Source="$(var.LSB.LogMonitor.Service_TargetDir)System.Text.Encodings.Web.dll" />
			</Component>
			<!-- System.Text.Json.dll -->
			<Component Id="SystemTextJsondll" Guid="{432C4CB4-A4AE-4AA0-A704-A4B707D99682}" Win64="yes">
				<File Id="SystemTextJsondll" Source="$(var.LSB.LogMonitor.Service_TargetDir)System.Text.Json.dll" />
			</Component>
			<!-- YamlDotNet.dll -->
			<Component Id="YamlDotNetdll" Guid="{FD9C936E-E941-4500-A48F-A75C071807AD}" Win64="yes">
				<File Id="YamlDotNetdll" Source="$(var.LSB.LogMonitor.Service_TargetDir)YamlDotNet.dll" />
			</Component>
		</ComponentGroup>
	</Fragment>
</Wix>
