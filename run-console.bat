@echo off
echo ========================================
echo LSB Log Monitor - Console Application
echo ========================================
echo.

echo Building application...
dotnet build LSB.LogMonitor.Service\LSB.LogMonitor.Service.csproj -c Release
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Build failed
    pause
    exit /b 1
)

echo.
echo Starting LSB Log Monitor Console Application...
echo.

cd LSB.LogMonitor.Service\bin\Release\net8.0
LSB.LogMonitor.Service.exe

echo.
echo Application stopped.
pause
