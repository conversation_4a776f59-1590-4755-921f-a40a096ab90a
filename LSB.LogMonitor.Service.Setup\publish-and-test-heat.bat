@echo off
echo ========================================
echo Publish and Test Heat
echo ========================================
echo.

echo [1/3] Publishing service project...
msbuild "..\LSB.LogMonitor.Service\LSB.LogMonitor.Service.csproj" /p:Configuration=Release /p:Platform="Any CPU" /t:Publish /v:minimal
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Failed to publish service project
    pause
    exit /b 1
)

echo.
echo [2/3] Checking publish directory...
set PUBLISH_DIR=..\LSB.LogMonitor.Service\bin\Release\net8.0\publish

if not exist "%PUBLISH_DIR%" (
    echo ERROR: Publish directory still not found after publish
    pause
    exit /b 1
)

echo ✅ Publish directory found: %PUBLISH_DIR%
echo.

echo Files in publish directory:
dir "%PUBLISH_DIR%" /b | findstr /v "Scripts"
echo.

echo [3/3] Testing Heat harvesting...
heat dir "%PUBLISH_DIR%" -cg ServiceFiles -gg -scom -sreg -sfrag -srd -dr INSTALLFOLDER -out ServiceFiles_Test.wxs -t ServiceFiles.xslt

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Heat failed
    pause
    exit /b 1
)

echo.
echo ✅ Heat completed successfully!
echo Generated file: ServiceFiles_Test.wxs
echo.

if exist "ServiceFiles_Test.wxs" (
    echo Checking component count...
    for /f %%i in ('findstr /c:"<Component" ServiceFiles_Test.wxs') do set COMPONENT_COUNT=%%i
    echo Found components in generated file
    echo.
    
    echo Checking for service installation...
    findstr "ServiceInstall" ServiceFiles_Test.wxs >nul
    if %ERRORLEVEL%==0 (
        echo ✅ Service installation found in generated file
    ) else (
        echo ⚠️  Service installation not found - XSLT transform may need adjustment
    )
    
    echo.
    echo Sample of generated content:
    echo [First 30 lines]
    more +1 ServiceFiles_Test.wxs | findstr /n ".*" | findstr "^[1-9]:" | findstr "^[12][0-9]:" | findstr "^3[0-9]:"
) else (
    echo ❌ ServiceFiles_Test.wxs not generated
)

echo.
echo Heat test completed!
pause
