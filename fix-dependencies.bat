@echo off
echo ========================================
echo Fix Dependencies for Console Application
echo ========================================
echo.

echo This script will help you fix the missing dependencies issue.
echo.

echo The following packages have been added to the project file:
echo.
echo Core Dependencies:
echo - Microsoft.Extensions.Hosting
echo - Microsoft.Extensions.DependencyInjection
echo - Microsoft.Extensions.DependencyInjection.Abstractions
echo.
echo Configuration:
echo - Microsoft.Extensions.Configuration
echo - Microsoft.Extensions.Configuration.Json
echo - Microsoft.Extensions.Configuration.EnvironmentVariables
echo - Microsoft.Extensions.Configuration.Binder
echo.
echo Logging:
echo - Microsoft.Extensions.Logging
echo - Microsoft.Extensions.Logging.Abstractions
echo - Microsoft.Extensions.Logging.Console
echo.
echo Options:
echo - Microsoft.Extensions.Options
echo - Microsoft.Extensions.Options.ConfigurationExtensions
echo.
echo Other:
echo - Microsoft.Extensions.Http
echo - System.Management
echo - System.Diagnostics.PerformanceCounter
echo - System.Text.Json
echo - YamlDotNet
echo.

echo ========================================
echo Next Steps:
echo ========================================
echo.
echo 1. Open Visual Studio or VS Code
echo 2. Open the LSB.LogMonitor.Service.sln solution
echo 3. Restore NuGet packages:
echo    - In Visual Studio: Right-click solution ^> Restore NuGet Packages
echo    - Or run: dotnet restore
echo.
echo 4. Build the project:
echo    - In Visual Studio: Build ^> Build Solution
echo    - Or run: dotnet build LSB.LogMonitor.Service -c Release
echo.
echo 5. Test the console application:
echo    - Run: run-console.bat
echo    - Or: dotnet run --project LSB.LogMonitor.Service
echo.

echo ========================================
echo Troubleshooting:
echo ========================================
echo.
echo If you still get "type not found" errors:
echo.
echo 1. Check that all using statements are present:
echo    - using Microsoft.Extensions.Hosting;
echo    - using Microsoft.Extensions.Logging;
echo    - using Microsoft.Extensions.Configuration;
echo    - using Microsoft.Extensions.DependencyInjection;
echo.
echo 2. Verify project file has all PackageReference entries
echo.
echo 3. Clean and rebuild:
echo    - dotnet clean
echo    - dotnet restore
echo    - dotnet build
echo.
echo 4. Check .NET version:
echo    - dotnet --version
echo    - Should be 8.0.x or higher
echo.

pause
