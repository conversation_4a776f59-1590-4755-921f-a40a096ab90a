using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using LSB.LogMonitor.Services;
using LSB.LogMonitor.Service.Models;
using LSB.LogMonitor.Service.Configuration;

namespace LSB.LogMonitor.Service
{
    public class QuickTest
    {
        public static async Task RunTest()
        {
            Console.WriteLine("🔧 LSB Log Monitor Service Quick Test");
            Console.WriteLine("====================================");

            try
            {
                // Build configuration
                var configuration = new ConfigurationBuilder()
                    .SetBasePath(Directory.GetCurrentDirectory())
                    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                    .Build();

                // Setup DI container
                var services = new ServiceCollection();
                services.AddSingleton<IConfiguration>(configuration);
                services.AddLogging(builder =>
                {
                    builder.AddConsole();
                    builder.SetMinimumLevel(LogLevel.Information);
                });

                // Configure options
                services.Configure<LogMonitorOptions>(configuration.GetSection(LogMonitorOptions.SectionName));
                services.AddSingleton<IConfigService, ConfigService>();
                services.AddSingleton<ILogService, LogService>();
                services.AddSingleton<ITelegramService, TelegramService>();

                var serviceProvider = services.BuildServiceProvider();

                var configService = serviceProvider.GetRequiredService<IConfigService>();
                var logService = serviceProvider.GetRequiredService<ILogService>();
                var telegramService = serviceProvider.GetRequiredService<ITelegramService>();

                Console.WriteLine("\n1. Testing Config Service...");
                var accName = await configService.GetAccNameAsync();
                Console.WriteLine($"✅ AccName from config: {accName}");

                Console.WriteLine("\n2. Testing Telegram Service...");
                await telegramService.SendTestMessageAsync();
                Console.WriteLine("✅ Telegram test message sent!");

                Console.WriteLine("\n2. Testing Log Service...");
                var clientNames = await logService.GetAvailableClientNamesAsync();
                Console.WriteLine($"📋 Found clients: {string.Join(", ", clientNames)}");

                var today = DateTime.Today;
                Console.WriteLine($"\n3. Checking logs for: {today:yyyy-MM-dd}");

                var clientsWithLogs = new List<ClientLogInfo>();

                foreach (var clientName in clientNames)
                {
                    Console.WriteLine($"   Checking: {clientName}");
                    var logsExist = await logService.LogsExistAsync(today, clientName);
                    Console.WriteLine($"   Logs exist: {logsExist}");

                    if (logsExist)
                    {
                        var summary = await logService.GenerateLogSummaryAsync(today, clientName);
                        Console.WriteLine($"   📁 Files: {summary.LogFiles.Count}, 📝 Entries: {summary.TotalLogEntries}, 🔴 Errors: {summary.HealthSummary?.TotalErrors ?? 0}");

                        clientsWithLogs.Add(new ClientLogInfo
                        {
                            ClientName = clientName,
                            LogFileCount = summary.LogFiles.Count,
                            Summary = summary,
                            Date = today
                        });
                    }
                }

                Console.WriteLine($"\n4. Clients with logs: {clientsWithLogs.Count}");

                if (clientsWithLogs.Any())
                {
                    Console.WriteLine("\n5. Sending notification...");
                    await telegramService.SendLogNotificationAsync(clientsWithLogs, today);
                    Console.WriteLine("✅ Notification sent!");
                }
                else
                {
                    Console.WriteLine("ℹ️ No logs found");
                }

                Console.WriteLine("\n🎉 Test completed successfully!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Test failed: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }
    }
}
