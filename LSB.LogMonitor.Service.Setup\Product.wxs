<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">
	<Product Id="*"
			 Name="LSB Log Monitor Service"
			 Language="1033"
			 Codepage="1252"
			 Version="*******"
			 Manufacturer="LSB"
			 UpgradeCode="{EB8A2174-7977-45CB-8EDB-936FBC25378E}">
		<Package InstallerVersion="200"
				 Compressed="yes"
				 InstallScope="perMachine"
				 Platform="x64"
				 Description="LSB Log Monitor Service - Automated log monitoring and notification system" />
		<Media Id="1" Cabinet="files.cab" EmbedCab="yes" />

		<!-- AUTO UPDATE SUPPORT -->
		<MajorUpgrade DowngradeErrorMessage="A newer version is already installed."
					  Schedule="afterInstallInitialize" />

		<!-- DIRECTORY STRUCTURE -->
		<Directory Id="TARGETDIR" Name="SourceDir">
			<Directory Id="ProgramFiles64Folder">
				<Directory Id="CompanyFolder" Name="LSB">
					<Directory Id="INSTALLFOLDER" Name="LogMonitor" />
				</Directory>
			</Directory>

			<!-- Start Menu -->
			<Directory Id="ProgramMenuFolder">
				<Directory Id="ApplicationProgramsFolder" Name="LSB Log Monitor" />
			</Directory>

			<!-- Desktop -->
			<Directory Id="DesktopFolder" Name="Desktop" />
		</Directory>

		<!-- FEATURES -->
		<Feature Id="Complete" Level="1" Title="LSB Log Monitor Service" Description="Complete installation of LSB Log Monitor Service">
			<ComponentGroupRef Id="ServiceFiles" />
			<ComponentRef Id="StartMenuShortcut" />
			<ComponentRef Id="DesktopShortcut" />
		</Feature>

		<!-- SHORTCUTS -->
		<Component Id="StartMenuShortcut" Directory="ApplicationProgramsFolder" Guid="*">
			<Shortcut Id="ApplicationStartMenuShortcut"
					  Name="LSB Log Monitor"
					  Description="LSB Log Monitor Console Application"
					  Target="[INSTALLFOLDER]LSB.LogMonitor.Service.exe"
					  WorkingDirectory="INSTALLFOLDER"
					  Icon="AppIcon" />
			<RemoveFolder Id="ApplicationProgramsFolder" On="uninstall"/>
			<RegistryValue Root="HKCU" Key="Software\LSB\LogMonitor"
						   Name="StartMenu" Type="integer" Value="1" KeyPath="yes"/>
		</Component>

		<Component Id="DesktopShortcut" Directory="DesktopFolder" Guid="*">
			<Shortcut Id="ApplicationDesktopShortcut"
					  Name="LSB Log Monitor"
					  Description="LSB Log Monitor Console Application"
					  Target="[INSTALLFOLDER]LSB.LogMonitor.Service.exe"
					  WorkingDirectory="INSTALLFOLDER"
					  Icon="AppIcon" />
			<RegistryValue Root="HKCU" Key="Software\LSB\LogMonitor"
						   Name="Desktop" Type="integer" Value="1" KeyPath="yes"/>
		</Component>

		<!-- ICON -->
		<Icon Id="AppIcon" SourceFile="app.ico" />

		<!-- REGISTRY ENTRIES -->
		<CustomAction Id="SetInstallPath"
					  Directory="INSTALLFOLDER"
					  ExeCommand='reg add "HKLM\SOFTWARE\LSB\LogMonitor" /v "InstallPath" /d "[INSTALLFOLDER]" /f'
					  Execute="deferred"
					  Impersonate="no" />

		<InstallExecuteSequence>
			<Custom Action="SetInstallPath" After="InstallFiles">NOT Installed</Custom>
		</InstallExecuteSequence>
	</Product>

	<!-- ServiceFiles ComponentGroup will be generated automatically by Heat -->
	<!-- Heat will scan the publish directory and create all necessary components -->
	<!-- XSLT transform will add service installation to the .exe file -->

	<!-- Note: ServiceFiles.wxs will be generated by Heat tool during build -->
	<!-- It will contain all DLLs and files from the publish directory -->
</Wix>