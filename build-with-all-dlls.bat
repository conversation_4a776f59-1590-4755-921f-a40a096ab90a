@echo off
echo ========================================
echo Build with ALL DLLs - Complete Guide
echo ========================================
echo.

echo This script will ensure you get ALL necessary DLLs in your installer.
echo.

echo [STEP 1] Cleaning previous builds...
if exist "LSB.LogMonitor.Service\bin" (
    rmdir /s /q "LSB.LogMonitor.Service\bin"
    echo ✅ Removed bin directory
)
if exist "LSB.LogMonitor.Service\obj" (
    rmdir /s /q "LSB.LogMonitor.Service\obj"
    echo ✅ Removed obj directory
)

echo.
echo [STEP 2] Restoring packages with all dependencies...
echo.
echo Project file now includes ALL required packages:
echo ✅ Microsoft.Extensions.Hosting
echo ✅ Microsoft.Extensions.DependencyInjection
echo ✅ Microsoft.Extensions.Configuration
echo ✅ Microsoft.Extensions.Logging
echo ✅ Microsoft.Extensions.Options
echo ✅ System.Text.Json
echo ✅ YamlDotNet
echo ✅ And all their dependencies...

dotnet restore LSB.LogMonitor.Service\LSB.LogMonitor.Service.csproj >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Package restore failed
    echo.
    echo Try in Visual Studio:
    echo 1. Right-click solution ^> Restore NuGet Packages
    echo 2. Build ^> Clean Solution
    echo 3. Build ^> Rebuild Solution
    pause
    exit /b 1
)
echo ✅ Packages restored successfully

echo.
echo [STEP 3] Publishing with ALL dependencies...

echo.
echo Option 1: Framework-dependent (requires .NET 8 on target machine)
dotnet publish LSB.LogMonitor.Service\LSB.LogMonitor.Service.csproj -c Release --self-contained false -o "publish-framework"
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Framework-dependent publish failed
    pause
    exit /b 1
)
echo ✅ Framework-dependent publish completed

echo.
echo Option 2: Self-contained (includes .NET runtime - larger but standalone)
dotnet publish LSB.LogMonitor.Service\LSB.LogMonitor.Service.csproj -c Release -r win-x64 --self-contained true -o "publish-standalone"
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Self-contained publish failed
    pause
    exit /b 1
)
echo ✅ Self-contained publish completed

echo.
echo [STEP 4] Checking dependencies...

echo.
echo Framework-dependent publish:
if exist "publish-framework\LSB.LogMonitor.Service.exe" (
    echo ✅ Main executable found
    for %%A in ("publish-framework\LSB.LogMonitor.Service.exe") do echo    Size: %%~zA bytes
) else (
    echo ❌ Main executable not found
)

for /f %%i in ('dir /b "publish-framework\*.dll" 2^>nul ^| find /c /v ""') do (
    echo ✅ Found %%i DLL files in framework-dependent build
)

echo.
echo Self-contained publish:
if exist "publish-standalone\LSB.LogMonitor.Service.exe" (
    echo ✅ Main executable found
    for %%A in ("publish-standalone\LSB.LogMonitor.Service.exe") do echo    Size: %%~zA bytes
) else (
    echo ❌ Main executable not found
)

for /f %%i in ('dir /b "publish-standalone\*.dll" 2^>nul ^| find /c /v ""') do (
    echo ✅ Found %%i DLL files in self-contained build
)

echo.
echo [STEP 5] Testing console application...

echo.
echo Testing framework-dependent build:
if exist "publish-framework\LSB.LogMonitor.Service.exe" (
    echo Starting test...
    cd publish-framework
    timeout /t 2 /nobreak >nul
    start /b LSB.LogMonitor.Service.exe --test
    timeout /t 3 /nobreak >nul
    taskkill /f /im LSB.LogMonitor.Service.exe >nul 2>&1
    cd ..
    echo ✅ Framework-dependent build test completed
) else (
    echo ❌ Cannot test - executable not found
)

echo.
echo [STEP 6] Building installer with Heat...

echo.
echo Copying framework-dependent build to standard publish location...
if exist "LSB.LogMonitor.Service\bin\Release\net8.0\publish" (
    rmdir /s /q "LSB.LogMonitor.Service\bin\Release\net8.0\publish"
)
mkdir "LSB.LogMonitor.Service\bin\Release\net8.0\publish" 2>nul
xcopy "publish-framework\*" "LSB.LogMonitor.Service\bin\Release\net8.0\publish\" /s /e /y >nul

echo ✅ Files copied to standard publish location

echo.
echo Now building installer with Heat...
echo Heat will automatically include ALL DLLs from publish directory.

if exist "LSB.LogMonitor.Service.Setup\LSB.LogMonitor.Service.Setup.wixproj" (
    echo Building WiX installer...
    msbuild LSB.LogMonitor.Service.Setup\LSB.LogMonitor.Service.Setup.wixproj /p:Configuration=Release >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo ✅ WiX installer built successfully
        if exist "LSB.LogMonitor.Service.Setup\bin\Release\*.msi" (
            for %%A in ("LSB.LogMonitor.Service.Setup\bin\Release\*.msi") do (
                echo ✅ MSI file: %%~nxA
                echo    Size: %%~zA bytes
            )
        )
    ) else (
        echo ⚠️ WiX build failed - you may need to build in Visual Studio
    )
) else (
    echo ⚠️ WiX project not found - installer not built
)

echo.
echo ========================================
echo BUILD SUMMARY
echo ========================================
echo.

echo ✅ FRAMEWORK-DEPENDENT BUILD (Recommended):
echo    Location: publish-framework\
echo    Requires: .NET 8.0 Runtime on target machine
echo    Size: Smaller
echo    DLLs: Application dependencies only

echo.
echo ✅ SELF-CONTAINED BUILD (Standalone):
echo    Location: publish-standalone\
echo    Requires: Nothing on target machine
echo    Size: Larger (~100MB+)
echo    DLLs: Application + .NET Runtime

echo.
echo ✅ INSTALLER (if WiX available):
echo    Location: LSB.LogMonitor.Service.Setup\bin\Release\
echo    Contains: All DLLs automatically included by Heat
echo    Install: Double-click MSI file

echo.
echo 🎯 DEPLOYMENT OPTIONS:
echo.
echo 1. Copy publish-framework\ to target machine (needs .NET 8)
echo 2. Copy publish-standalone\ to target machine (standalone)
echo 3. Use MSI installer (if available)
echo.
echo All builds include ALL necessary DLLs!

echo.
pause
