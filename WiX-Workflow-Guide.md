# 🔧 WiX Workflow Guide - C<PERSON><PERSON> làm việc với WiX và Heat Tool

## 🎯 **Hiểu về WiX Files:**

### **Files bạn CÓ THỂ edit:**
- ✅ `Product.wxs` - C<PERSON>u hình installer chính
- ✅ `ServiceFiles.xslt` - Transform rules cho Heat
- ✅ `LSB.LogMonitor.Service.Setup.wixproj` - Build configuration

### **Files KHÔNG được edit:**
- ❌ `ServiceFiles.wxs` - Generated by Heat (sẽ bị overwrite)
- ❌ Files trong `publish\` - Generated by dotnet publish

---

## 🔧 **Cách customize WiX installer:**

### **1. Thay đổi thông tin installer (Product.wxs):**

```xml
<Product Id="*"
         Name="LSB Console Application"           <!-- Tên hiển thị -->
         Language="1033"
         Version="1.0.0.0"                       <!-- Version -->
         Manufacturer="LSB Company"               <!-- Nhà sản xuất -->
         UpgradeCode="{EB8A2174-7977-45CB-8EDB-936FBC25378E}">

<!-- Thay đổi thư mục cài đặt -->
<Directory Id="TARGETDIR" Name="SourceDir">
    <Directory Id="ProgramFiles64Folder">
        <Directory Id="CompanyFolder" Name="LSB">
            <Directory Id="INSTALLFOLDER" Name="LogMonitor" />  <!-- Tên thư mục -->
        </Directory>
    </Directory>
</Directory>
```

### **2. Thêm/bỏ Custom Actions:**

```xml
<!-- Thêm registry entries -->
<CustomAction Id="AddRegistryEntry"
              Directory="INSTALLFOLDER"
              ExeCommand='reg add "HKLM\SOFTWARE\LSB" /v "InstallPath" /d "[INSTALLFOLDER]" /f'
              Execute="deferred"
              Impersonate="no" />

<!-- Tạo shortcuts -->
<CustomAction Id="CreateShortcut"
              Directory="INSTALLFOLDER"
              ExeCommand='powershell -Command "Create-Shortcut"'
              Execute="deferred"
              Impersonate="no" />
```

### **3. Customize Heat behavior (ServiceFiles.xslt):**

```xml
<!-- Thêm service installation cho console app -->
<xsl:template match="wix:Component[wix:File[@Source and contains(@Source, 'LSB.LogMonitor.Service.exe')]]">
  <xsl:copy>
    <xsl:apply-templates select="@*"/>
    <xsl:apply-templates select="wix:File"/>
    
    <!-- Không cần ServiceInstall cho console app -->
    <!-- Chỉ cần file executable -->
  </xsl:copy>
</xsl:template>

<!-- Thêm config file với NeverOverwrite -->
<xsl:template match="wix:Component[wix:File[@Source and contains(@Source, 'appsettings.json')]]">
  <xsl:copy>
    <xsl:apply-templates select="@*"/>
    <wix:File>
      <xsl:apply-templates select="wix:File/@*"/>
      <xsl:attribute name="NeverOverwrite">yes</xsl:attribute>
    </wix:File>
  </xsl:copy>
</xsl:template>
```

---

## 🚀 **Build Process:**

### **Automatic Build (Khuyến nghị):**
```cmd
# Build với Heat tự động
build-with-heat.bat

# Hoặc trong Visual Studio
Right-click LSB.LogMonitor.Service.Setup → Build
```

### **Manual Build Steps:**
```cmd
# 1. Publish console application
dotnet publish LSB.LogMonitor.Service -c Release -o publish

# 2. Run Heat to generate ServiceFiles.wxs
heat dir "publish" -cg ServiceFiles -gg -scom -sreg -sfrag -srd -dr INSTALLFOLDER -t ServiceFiles.xslt -out ServiceFiles.wxs

# 3. Compile WiX
candle Product.wxs ServiceFiles.wxs

# 4. Link to create MSI
light -out LSB.LogMonitor.Service.msi Product.wixobj ServiceFiles.wixobj
```

---

## 🎮 **Common Customizations:**

### **A. Console Application (không cần Windows Service):**

**Product.wxs:**
```xml
<!-- Bỏ ServiceInstall, chỉ cần files -->
<Feature Id="Complete" Level="1">
    <ComponentGroupRef Id="ServiceFiles" />
</Feature>

<!-- Thêm Start Menu shortcut -->
<Directory Id="ProgramMenuFolder">
    <Directory Id="ApplicationProgramsFolder" Name="LSB Log Monitor">
        <Component Id="ApplicationShortcut" Guid="*">
            <Shortcut Id="ApplicationStartMenuShortcut"
                      Name="LSB Log Monitor"
                      Description="LSB Log Monitor Console"
                      Target="[INSTALLFOLDER]LSB.LogMonitor.Service.exe"
                      WorkingDirectory="INSTALLFOLDER"/>
            <RemoveFolder Id="ApplicationProgramsFolder" On="uninstall"/>
            <RegistryValue Root="HKCU" Key="Software\LSB\LogMonitor"
                           Name="installed" Type="integer" Value="1" KeyPath="yes"/>
        </Component>
    </Directory>
</Directory>
```

### **B. Thêm Desktop Shortcut:**

```xml
<Directory Id="DesktopFolder" Name="Desktop">
    <Component Id="DesktopShortcut" Guid="*">
        <Shortcut Id="DesktopShortcut"
                  Name="LSB Log Monitor"
                  Target="[INSTALLFOLDER]LSB.LogMonitor.Service.exe"
                  WorkingDirectory="INSTALLFOLDER"/>
        <RegistryValue Root="HKCU" Key="Software\LSB\LogMonitor"
                       Name="desktop" Type="integer" Value="1" KeyPath="yes"/>
    </Component>
</Directory>
```

### **C. Preserve Config Files:**

**ServiceFiles.xslt:**
```xml
<!-- Không overwrite config files -->
<xsl:template match="wix:Component[wix:File[@Source and contains(@Source, '.json')]]">
  <xsl:copy>
    <xsl:apply-templates select="@*"/>
    <wix:File>
      <xsl:apply-templates select="wix:File/@*"/>
      <xsl:attribute name="NeverOverwrite">yes</xsl:attribute>
    </wix:File>
  </xsl:copy>
</xsl:template>
```

---

## 🔧 **Debugging WiX:**

### **1. Check Heat Output:**
```cmd
# Test Heat manually
heat dir "publish" -cg ServiceFiles -gg -scom -sreg -sfrag -srd -dr INSTALLFOLDER -out ServiceFiles_Test.wxs

# Check generated file
notepad ServiceFiles_Test.wxs
```

### **2. Verbose Build:**
```cmd
# Build with verbose output
msbuild LSB.LogMonitor.Service.Setup.wixproj /p:Configuration=Release /v:detailed
```

### **3. MSI Logging:**
```cmd
# Install with logging
msiexec /i LSB.LogMonitor.Service.Setup.msi /l*v install.log

# Check log file
notepad install.log
```

---

## 📋 **Best Practices:**

### **✅ DO:**
- Use Heat for automatic file harvesting
- Keep Product.wxs simple and focused
- Use XSLT transforms for Heat customization
- Version your installers properly
- Test on clean machines

### **❌ DON'T:**
- Edit ServiceFiles.wxs manually
- Hardcode file paths
- Forget to update version numbers
- Skip testing uninstall process
- Mix manual and Heat-generated components

---

## 🎯 **For Console Application:**

Since you switched to console application, you probably want:

1. **Remove Windows Service installation**
2. **Add Start Menu shortcuts**
3. **Preserve configuration files**
4. **Simple file deployment**

The Heat approach is still perfect for this - it will automatically include all your console app dependencies without manual maintenance!
