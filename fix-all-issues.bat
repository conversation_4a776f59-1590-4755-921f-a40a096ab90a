@echo off
echo ========================================
echo Fix All Console Application Issues
echo ========================================
echo.

echo This script will fix all common issues with the console application:
echo 1. Package version conflicts
echo 2. Missing dependencies
echo 3. Build errors
echo.

echo [STEP 1] Checking project file...
if not exist "LSB.LogMonitor.Service\LSB.LogMonitor.Service.csproj" (
    echo ❌ Project file not found!
    echo Please run this script from the solution root directory.
    pause
    exit /b 1
)
echo ✅ Project file found

echo.
echo [STEP 2] Package version fixes applied:
echo ✅ Microsoft.Extensions.Configuration.Json: 8.0.1
echo ✅ Microsoft.Extensions.DependencyInjection.Abstractions: 8.0.2
echo ✅ All other packages: Compatible versions

echo.
echo [STEP 3] Cleaning previous builds...
if exist "LSB.LogMonitor.Service\bin" (
    rmdir /s /q "LSB.LogMonitor.Service\bin"
    echo ✅ Removed bin directory
)
if exist "LSB.LogMonitor.Service\obj" (
    rmdir /s /q "LSB.LogMonitor.Service\obj"
    echo ✅ Removed obj directory
)

echo.
echo [STEP 4] Checking .NET SDK...
dotnet --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ .NET SDK not found!
    echo.
    echo Please install .NET 8.0 SDK from:
    echo https://dotnet.microsoft.com/download
    echo.
    echo Then restart this script.
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('dotnet --version 2^>nul') do set DOTNET_VERSION=%%i
echo ✅ .NET SDK found: %DOTNET_VERSION%

echo.
echo [STEP 5] Restoring NuGet packages...
dotnet restore LSB.LogMonitor.Service\LSB.LogMonitor.Service.csproj --verbosity minimal
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Package restore failed!
    echo.
    echo Try these alternatives:
    echo 1. Open Visual Studio
    echo 2. Right-click solution ^> Restore NuGet Packages
    echo 3. Build ^> Clean Solution
    echo 4. Build ^> Rebuild Solution
    pause
    exit /b 1
)
echo ✅ Packages restored successfully

echo.
echo [STEP 6] Building project...
dotnet build LSB.LogMonitor.Service\LSB.LogMonitor.Service.csproj -c Release --verbosity minimal
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Build failed!
    echo.
    echo Common issues and solutions:
    echo.
    echo 1. Missing using statements:
    echo    - Check check-using-statements.md
    echo    - Add required using statements to .cs files
    echo.
    echo 2. Namespace issues:
    echo    - Ensure all classes are in correct namespaces
    echo    - Check interface implementations
    echo.
    echo 3. Try building in Visual Studio for better error messages
    pause
    exit /b 1
)
echo ✅ Build successful!

echo.
echo [STEP 7] Verifying output...
if exist "LSB.LogMonitor.Service\bin\Release\net8.0\LSB.LogMonitor.Service.exe" (
    echo ✅ Executable created successfully
    for %%A in ("LSB.LogMonitor.Service\bin\Release\net8.0\LSB.LogMonitor.Service.exe") do echo File size: %%~zA bytes
    
    echo.
    echo ✅ Checking dependencies...
    if exist "LSB.LogMonitor.Service\bin\Release\net8.0\Microsoft.Extensions.Hosting.dll" (
        echo ✅ Microsoft.Extensions.Hosting.dll found
    ) else (
        echo ⚠️ Microsoft.Extensions.Hosting.dll not found
    )
    
    if exist "LSB.LogMonitor.Service\bin\Release\net8.0\appsettings.json" (
        echo ✅ appsettings.json found
    ) else (
        echo ⚠️ appsettings.json not found
    )
) else (
    echo ❌ Executable not found!
    echo Build may have failed silently.
    pause
    exit /b 1
)

echo.
echo ========================================
echo 🎉 ALL ISSUES FIXED SUCCESSFULLY!
echo ========================================
echo.

echo ✅ Console application is ready to use!
echo.
echo Next steps:
echo.
echo 1. 🚀 Run the application:
echo    run-console.bat
echo.
echo 2. 🧪 Test the application:
echo    test-console.bat
echo.
echo 3. 🔧 Development mode:
echo    run-dev.bat
echo.
echo 4. 📦 Build for deployment:
echo    build-console.bat
echo.
echo 5. 📖 Read documentation:
echo    README-Console.md
echo.

echo The console application features:
echo - Interactive commands (q, test, status, help)
echo - Real-time logging output
echo - Graceful shutdown with Ctrl+C
echo - Easy deployment (just copy files)
echo.

set /p choice="Would you like to test the application now? (y/n): "
if /i "%choice%"=="y" (
    echo.
    echo Starting test...
    call test-console.bat
) else (
    echo.
    echo You can run the application anytime with: run-console.bat
)

echo.
pause
